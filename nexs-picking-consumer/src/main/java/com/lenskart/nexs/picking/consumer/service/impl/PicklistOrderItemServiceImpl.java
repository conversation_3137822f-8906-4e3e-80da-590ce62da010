package com.lenskart.nexs.picking.consumer.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableSet;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.request.BarcodeItemRequest;
import com.lenskart.nexs.ims.request.FetchStockDetailsRequest;
import com.lenskart.nexs.ims.response.FetchStockDetailsResponse;
import com.lenskart.nexs.picking.config.PickingConfig;
import com.lenskart.nexs.picking.connector.CatalogOpsConnector;
import com.lenskart.nexs.picking.connector.FMSConnector;
import com.lenskart.nexs.picking.connector.IMSConnector;
import com.lenskart.nexs.picking.connector.InventoryAdapterConnector;
import com.lenskart.nexs.picking.connector.WmsConnector;
import com.lenskart.nexs.picking.constant.Constant;
import com.lenskart.nexs.picking.consumer.config.DBConfig;
import com.lenskart.nexs.picking.consumer.config.GetProductDetailRejectionHandler;
import com.lenskart.nexs.picking.constant.DBEnums;
import com.lenskart.nexs.picking.consumer.constant.PickingConsumerConstant;
import com.lenskart.nexs.picking.consumer.service.PicklistOrderItemService;
import com.lenskart.nexs.picking.entity.*;
import com.lenskart.nexs.picking.enums.OrderState;
import com.lenskart.nexs.picking.enums.WaveStatus;
import com.lenskart.nexs.picking.payload.addverb.WavePickingItem;
import com.lenskart.nexs.picking.repository.readonly.PickingSummaryReadOnlyRepository;
import com.lenskart.nexs.picking.repository.writeonly.*;
import com.lenskart.nexs.picking.request.AdverbToManualPojo;
import com.lenskart.nexs.picking.response.Meta;
import com.lenskart.nexs.picking.service.AdverbService;
import com.lenskart.nexs.picking.service.SystemPreferenceService;
import com.lenskart.nexs.picking.dao.CacheDAO;
import com.lenskart.nexs.picking.dto.Actions;
import com.lenskart.nexs.picking.dto.ConditionDto;
import com.lenskart.nexs.picking.dto.PickingLocationDto;
import com.lenskart.nexs.picking.enums.JITType;
import com.lenskart.nexs.picking.esDocument.PicklistOrderItemDocument;
import com.lenskart.nexs.picking.exception.exception.PickingException;
import com.lenskart.nexs.picking.exception.exception.enums.PickingExceptionStatus;
import com.lenskart.nexs.picking.repository.readonly.CourierCutoffDetailRepository;
import com.lenskart.nexs.picking.request.MarkInventoryFoundRequest;
import com.lenskart.nexs.picking.response.Messages;
import com.lenskart.nexs.picking.service.RuleEngineService;
import com.lenskart.nexs.picking.service.impl.AddverbServiceImpl;
import com.lenskart.nexs.picking.util.AddverbUtils;
import com.lenskart.nexs.picking.util.NexsDateUtil;
import com.lenskart.nexs.picking.util.ObjectHelper;
import com.lenskart.nexs.picking.util.PickingCommonUtils;
import com.lenskart.nexs.picking.util.WavePayloadUtil;
import com.lenskart.nexs.wms.enums.FittingType;
import com.lenskart.nexs.wms.enums.FulfillableType;
import com.lenskart.nexs.wms.enums.ItemType;
import com.lenskart.nexs.wms.enums.OrderItemOperation;
import com.lenskart.nexs.wms.enums.OrderItemStatus;
import com.lenskart.nexs.wms.enums.ProcessingType;
import com.lenskart.nexs.wms.request.OrderItemPower;
import com.lenskart.nexs.wms.response.OrderStatusUpdateResponse;
import com.lenskart.nexs.wms.response.order.OrderDetailsResponse;
import com.lenskart.nexs.wms.response.order.OrderItemHeaderResponse;
import com.lenskart.nexs.wms.response.order.OrderItemResponse;
import com.lenskart.nexs.wms.response.order.ShipmentResponse;
import com.lenskart.nexs.wms.response.qc.Product;
import com.lenskart.nexscommonalerts.util.SlackMessageSender;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.lenskart.nexs.picking.constant.Constant.PICKING_CONSTANT.AUTOREPLENISHMENT;
import static com.lenskart.nexs.picking.constant.Constant.PICKING_DEFAULT.PINCODE_PRIORITY;
import static com.lenskart.nexs.picking.constant.Constant.PICKING_ORDER_TYPE_CONSTANT.DISTRIBUTED_ORDERS;
import static com.lenskart.nexs.picking.constant.Constant.PICKING_ORDER_TYPE_CONSTANT.DO_SO_ORDERS;
import static com.lenskart.nexs.picking.constant.Constant.PICKING_STATUS.HOLDED;
import static com.lenskart.nexs.wms.enums.OrderItemHeaderMetaDataKeys.ORDER_PRIORITY_TYPE;

@Service
public class PicklistOrderItemServiceImpl implements PicklistOrderItemService {

    private static final String CLASS_NAME = PicklistOrderItemServiceImpl.class.getSimpleName();
    private static final String DO_JIT_CHANNEL = "INT_BULKTOVENDOR";
    @CustomLogger
    @Setter(AccessLevel.PROTECTED)
    private Logger log;

    @Autowired
    private CatalogOpsConnector catalogOpsConnector;

    @Autowired
    WmsConnector wmsConnector;

    @Autowired
    private IMSConnector imsConnector;

    @Autowired
    private FMSConnector fmsConnector;

    @Autowired
    private GetProductDetailRejectionHandler getProductDetailRejectionHandler;

    @Autowired
    private SystemPreferenceService systemPreferenceService;

    @Autowired
    private CacheDAO cacheDAO;

    @Autowired
    private PicklistOrderItemRepository picklistOrderItemRepo;

    @Autowired
    private PickingDetailRepository pickingDetailRepository;

    @Autowired
    private PicklistOrderItemDocumentRepository picklistOrderItemDocumentRepository;

    @Autowired
    private PickingConfig pickingConfig;

    @Autowired
    private SyncInESServiceImpl syncInESServiceImpl;

    @Autowired
    private PicklistOrderItemLogsRepository picklistOrderItemLogsRepo;

    private PrescriptionsLenDetailRepository prescriptionsLenDetailRepository;
    @Autowired
    private WaveDetailRepository waveDetailRepository;
    @Autowired
    private WavePayloadUtil wavePayloadUtil;

    @Autowired
    private CourierCutoffDetailRepository courierCutoffDetailRepository;

    @Autowired
    private PicklistOrderItemMetaDataRepository picklistOrderItemMetaDataRepository;

    @Autowired
    private DBConfig dbConfig;

    @Autowired
    private PickingSummaryReadOnlyRepository pickingSummaryReadOnlyRepository;

    @Autowired
    private PickingSummaryRepository pickingSummaryRepository;

    @Autowired
    private AddverbServiceImpl adverbService;

    private Set<String> terminalStatus = new HashSet<>(Arrays.asList(OrderItemStatus.CANCELLED.name(),
            OrderItemStatus.REASSIGNED.name(), OrderItemStatus.POWER_CHANGED.name(),OrderItemStatus.DISPATCHED.name()));

    @Autowired
    private SlackMessageSender slackMessageSender;
    @Value("${picking-alert-slack-channel:supply-chain-alerts}")
    private String pickingAlertSlackChannel;

    @Autowired
    private PickingCommonUtils pickingCommonUtils;

    @Value("${addverb.picking.slots}")
    private int totalPickingSlots;

    @Autowired
    AddverbUtils addverbUtils;
    @Value("${change.cutoff.on.priority.required}")
    boolean allowCutOffOnPriority;
    @Value("${is.gold.max.cutOff.alter.required}")
    boolean isGoldMaxCutOffAlterAllowed;

    @Value("${fast.picking.redis.key.ttl}")
    private int fastPickingRedisKeyTTl;

    @Value("${set.priority.on.created.time}")
    boolean setPriorityOnCreatedAtTime;
    @Value("${p1.orders.start.time}")
    String pOneOrdersStartTime;
    @Value("${p1.orders.end.time}")
    String pOneOrdersEndTime;
    @Value("#{'${list.priority.for.cuttoff.change}'.split(',')}")
    List<Integer> alterCutOffPriorityList;
    @Value("#{'${list.priority.for.P11.cuttoff.change}'.split(',')}")
    List<Integer> alterCutOffPriorityP11List;

    @Value("${set.priority.on.citywise.cuttoff.time}")
    boolean setPriorityOnCitywiseCuttOff;

    @Value("${nexs.picking.enable.wms.priority}")
    private boolean enableWmsPriority;

    @Value("${nexs.picking.order.processing.time}")
    private int orderProcessingTimeBuffer;
    @Value("${nexs.picking.NDD.order.processing.time}")
    private int nddOrderProcessingTimeBuffer;
    @Value("${nexs.picking.NDD.goldMax.order.processing.time}")
    private int nddGoldMaxOrderProcessingTimeBuffer;
    @Value("${nexs.picking.NDD.gold.order.processing.time}")
    private int nddGoldOrderProcessingTimeBuffer;

    @Value("#{'${list.facility.for.qc.fail.priority}'.split(',')}")
    List<String> allowedFacilitiesForQcFailPriorityZero;

    @Value("#{'${list.facility.for.processingType.fr1.fr2:NXS2}'.split(',')}")
    List<String> allowedFacilitiesForLensFr1OrFr2;

    @Value("#{'${allowed.unff.facility.list:SGNXS1}'.split(',')}")
    List<String> allowedUnffFacilities;

    @Value("${nexs.picking.enable.ims.distinctLocationBarcode}")
    private boolean enableGetDistinctAsrsLocationBarcode;

    @Value("${is.ims.duplicate.call.allowed:false}")
    private boolean isIMSDuplicateCallAllowed;

    @Value("${is.redis.call.allowed.for.ims.location:true}")
    private boolean isRedisCallAllowedForIMSLocation;

    @Value("${pid.location.info.ttl.in.minutes:5}")
    private Long pidLocationInfoTTLInMinutes;
    @Value("#{'${alter.priority.for.lo.lp.facility.list}'.split(',')}")
    List<String> alterPriorityForLOLPFacilityList;

    @Value("${is.short.product.name.allowed:false}")
    boolean isShortProductNameAllowed;
    @Value("#{'${short.name.allowed.facility:NXS2}'.split(',')}")
    List<String> shortNameAllowedFacility;
    @Autowired
    PickingFetchAdditionalDataServiceImpl pickingFetchAdditionalDataService;

    private static Set<Integer> allowedPrioritiesForQcFail = ImmutableSet.of(10,11);

    @Autowired
    private InventoryAdapterConnector inventoryAdapterConnector;

    @Value("#{'${hold.status.allowed.list:0,1,4}'.split(',')}")
    private List<Integer> holdStatusAllowedList;

    @Value("${picking.priority.cutoff.configuration.enabled:false}")
    boolean pickingPriorityCutoffConfigurationEnabled;

    @Value("${nexs.auto.mark.not.found.enabled:false}")
    private boolean isAutoMarkNotFoundAllowed;

    @Autowired
    private RuleEngineService ruleEngineService;

    // Method to create, update picklistOrderItems using orderDetailResponse
    @Override
    @Transactional
    public void processOrderDetailsResponse(OrderDetailsResponse orderDetailsResponse, String rePickStatus, boolean isJIT, String jitType, String eventIdempotencyKey, boolean hhdPickingAllowed)throws Exception {
        log.info("Process Detail in Picklist Order Item using WMS Data {}, {} for wmsOrderCode - {}", orderDetailsResponse.getIncrementId(), rePickStatus, orderDetailsResponse.getOrderId());
        List<PicklistOrderItem> picklistOrderItemsList = new LinkedList<>();
        OrderItemHeaderResponse orderItemHeaderResponseList = orderDetailsResponse.getOrderItemHeaderResponse();
        List<OrderItemResponse> orderItemResponseList = orderItemHeaderResponseList.getOrderItemResponses();
        Set<Integer> productIdList = orderItemResponseList.stream().map(OrderItemResponse::getProduct_id).collect(Collectors.toSet());
        Integer noOfProduct = productIdList.size();
        boolean isBulkOrder = checkIfBulkOrder(orderDetailsResponse);

        String fr1OrFr2 = orderItemResponseList.stream()
                .map(OrderItemResponse::getProcessingType)
                .filter(processingType -> ProcessingType.FR1.equals(processingType) || ProcessingType.FR2.equals(processingType))
                .map(Enum::name)
                .findFirst()
                .orElse(null);
        String loyaltyTier = null;
        if(!(
                isAutoReplenishemntOrder(orderDetailsResponse)||
                        isBulkOrder ||
                        Constant.PICKING_CONSTANT.TRANSFER_SOURCE.equalsIgnoreCase(getPickingSource(orderDetailsResponse))))
              loyaltyTier = pickingFetchAdditionalDataService.findLoyaltyTierUsingCustomerId(orderDetailsResponse.getCustomerId());

        try {
            if (rePickStatus != null) {
                log.info("Update Detail in Picklist Order Item from WMS ,for incrementId {}, rePicking status {}",
                        orderDetailsResponse.getIncrementId(), rePickStatus);
                for (OrderItemResponse orderItemResponse : orderItemResponseList) {
                    String pickingSource = getPickingSource(orderDetailsResponse);
                    log.info("[PickListOrderItemsServiceImpl, processorderDetails] The picking source is {} for the wmsOrderItemId {} and processingType {}",pickingSource, orderItemResponse.getOrderItemId(), orderItemResponse.getProcessingType());

                    PicklistOrderItem picklistOrderItem =
                            picklistOrderItemRepo.findByWmsOrderItemIdAndPickingSource(orderItemResponse.getId().intValue(),pickingSource);

                    // Check if the picking event was already consumed
                    // Introduced due to ES issue causing redundant message updates
                    if(picklistOrderItem!=null && picklistOrderItem.getEventIdempotencyKey()!=null && eventIdempotencyKey!=null )
                    {
                        log.info("[PickListOrderItemsServiceImpl, processorderDetails] The eventIdempotencyKey reterived for picklistOdrderItemId {} is {}",picklistOrderItem.getId(),picklistOrderItem.getEventIdempotencyKey());
                        picklistOrderItem.setLegalOwner(orderItemHeaderResponseList.getLegalOwner());
                        if(eventIdempotencyKey.equalsIgnoreCase(picklistOrderItem.getEventIdempotencyKey())) {
                            log.info("[PickListOrderItemsServiceImpl, processorderDetails] The picklistOrderItems {} has already been processed for eventIdempotencyKey {} hence skipping it for 201 case.",picklistOrderItem,eventIdempotencyKey);
                            continue;
                         }
                    }

                    if (picklistOrderItem == null) {
                        log.info("PicklistOrderItem not found while rePicking. WmsOrderItemId: " + orderItemResponse.getId().intValue());
                        if (OrderItemStatus.POWER_CHANGED.name().equals(rePickStatus)) {
                            picklistOrderItem = createPicklistOrderItem(orderItemResponse, orderDetailsResponse,
                                    noOfProduct, rePickStatus, isJIT,jitType,eventIdempotencyKey,pickingSource,isBulkOrder,fr1OrFr2,loyaltyTier);
                            picklistOrderItem.setLegalOwner(orderItemHeaderResponseList.getLegalOwner());
                        } else {
                            throw new PickingException("PicklistOrderItem not found while rePicking. WmsOrderItemId: "
                                    + orderItemResponse.getId().intValue());
                        }

                    } else {
                        log.info("Already existing picklist data for wms order id: {}",
                                orderItemResponse.getId().intValue());
                        picklistOrderItem = updatePicklistOrderItem(picklistOrderItem, orderItemResponse,
                                orderDetailsResponse, rePickStatus,eventIdempotencyKey,isBulkOrder,false,loyaltyTier,
                                jitType,fr1OrFr2);
                        picklistOrderItem.setLegalOwner(orderItemHeaderResponseList.getLegalOwner());
                        log.info("Picklist order item details successfully updated for wms order id: {}",
                                orderItemResponse.getId().intValue());

                        picklistOrderItemsList.add(picklistOrderItem);
                        //If the item is present in the picklist_order_item check if it is for power change

                    }

                }
                fetchPicklistOrderDetailsFromExternalService(productIdList, picklistOrderItemsList, rePickStatus, false);

            } else {
                log.info("Save Detail in Picklist Order Item from WMS ,for incrementId {}, rePicking status {}",
                        orderDetailsResponse.getIncrementId(), rePickStatus);
                boolean isItemUnffToFulfillable = false;
                for (OrderItemResponse orderItemResponse : orderItemResponseList) {
                    String pickingSource = getPickingSource(orderDetailsResponse);
                    PicklistOrderItem picklistOrderItemDetail =
                            picklistOrderItemRepo.findByWmsOrderItemIdAndPickingSource(orderItemResponse.getId().intValue(),pickingSource);
                    if(null == picklistOrderItemDetail) {
                        picklistOrderItemDetail = createPicklistOrderItem(orderItemResponse, orderDetailsResponse, noOfProduct, rePickStatus, isJIT, jitType, eventIdempotencyKey, pickingSource, isBulkOrder, fr1OrFr2,loyaltyTier);
                        log.info("Item successfully stored in Picklist Order Item, {}", picklistOrderItemDetail.getId());
                    }
                    else {
                        picklistOrderItemDetail = updatePicklistOrderItem(picklistOrderItemDetail, orderItemResponse,
                                orderDetailsResponse, null, eventIdempotencyKey, isBulkOrder,true,loyaltyTier,
                                jitType,fr1OrFr2);

                        if(FulfillableType.NON_FULFILLABLE.equals(orderItemResponse.getFulfillableType()) || HOLDED.equals(picklistOrderItemDetail.getOrderState())) {
                            if (allowedUnffFacilities.contains(orderItemResponseList.get(0).getFacilityCode())) {
                                deleteAllDocumentFromEsForShipment(picklistOrderItemDetail.getShipmentId());
                            } else {
                                deleteDocumentFromEs(picklistOrderItemDetail);
                            }
                        }
                        else if(!isItemUnffToFulfillable && FulfillableType.NON_FULFILLABLE.equals(picklistOrderItemDetail.getFullFillType())
                                && FulfillableType.FULFILLABLE.equals(orderItemResponse.getFulfillableType())){
                            isItemUnffToFulfillable = true;
                        }
                        updatePickingDetails(picklistOrderItemDetail,orderItemResponse.getFulfillableType());
                    }
                    picklistOrderItemsList.add(picklistOrderItemDetail);
                }
                boolean areAllItemsFullFillable = orderItemResponseList.stream().allMatch(item -> FulfillableType.FULFILLABLE.equals(item.getFulfillableType()));

                allItemFulfillableAfterUnff(orderDetailsResponse, orderItemResponseList, isItemUnffToFulfillable);

                if(!areAllItemsFullFillable) {
                  //checking TLP order if all item fullfillable check false.
                  areAllItemsFullFillable = unffTLPOrder(picklistOrderItemsList);
                }

                if(areAllItemsFullFillable)
                    fetchPicklistOrderDetailsFromExternalService(productIdList, picklistOrderItemsList, rePickStatus, hhdPickingAllowed);
            }
        } catch (Exception e) {
            log.error("PicklistOrderItemServiceImpl.processOrderDetailsResponse.Exception For wmsOrderCode - " + orderDetailsResponse.getOrderId() + ", incrementId - " + orderDetailsResponse.getIncrementId() + ", Error - " + e.getMessage(), e);
            throw e;
        }
    }

    private void allItemFulfillableAfterUnff(OrderDetailsResponse orderDetailsResponse,
                                             List<OrderItemResponse> orderItemResponseList,
                                             boolean isItemUnffToFulfillable) {
        try {
            log.info("allItemFulfillableAfterUnff isItemUnffToFulfillable {} | orderDetailsResponse :{}",
                    isItemUnffToFulfillable, orderDetailsResponse.getOrderItemHeaderResponse().getShippingPackageId());
            List<PicklistOrderItem> shipments=new ArrayList<>();
            if(isItemUnffToFulfillable && allowedUnffFacilities.contains(orderItemResponseList.get(0).getFacilityCode()) && isAllItemFulfillable(orderDetailsResponse.getOrderItemHeaderResponse().getShippingPackageId(),shipments)) {
                log.info("allItemFulfillableAfterUnff Movel all item to fulfullable status: {}",
                        orderDetailsResponse.getOrderItemHeaderResponse().getShippingPackageId());
                List<PicklistOrderItemDocument> picklistOrderItemDocumentList = new ArrayList<>();
                List<PicklistOrderItem> unpickedShipemts =
                        shipments.stream().filter(picklistOrderItem -> picklistOrderItem.getStatus() < 2).collect(Collectors.toList());
                log.info("shipment - {} |  - {}", shipments.size(), unpickedShipemts.size());
                for (PicklistOrderItem picklistOrderItem :
                        unpickedShipemts) {
                    syncInESServiceImpl.savePicklistOrderItemInES(picklistOrderItem,
                            picklistOrderItemDocumentList);
                }
                if (!CollectionUtils.isEmpty(picklistOrderItemDocumentList))
                    picklistOrderItemDocumentRepository.saveAll(picklistOrderItemDocumentList);
                log.info("allItemFulfillableAfterUnff Movel all item to fulfullable status compelted: {}",
                        orderDetailsResponse.getOrderItemHeaderResponse().getShippingPackageId());
            }
        }catch (Exception ex){
            log.error("allItemFulfillableAfterUnff error "+ex.getMessage(),ex);
        }
    }

    private boolean isAllItemFulfillable(String shippingPackageId,List<PicklistOrderItem> shipments) {
        shipments = picklistOrderItemRepo.findByShipmentId(shippingPackageId);
        return shipments.stream().allMatch(item-> FulfillableType.FULFILLABLE.equals(item.getFullFillType()));
    }

    @Override
    public void deleteDocumentFromEs(PicklistOrderItem picklistOrderItems) {
        PicklistOrderItemDocument picklistOrderItemDocument = picklistOrderItemDocumentRepository.findByPicklistOrderItemId(picklistOrderItems.getId());
        if(!ObjectUtils.isEmpty(picklistOrderItemDocument) && null!=picklistOrderItemDocument) {
            picklistOrderItemDocumentRepository.delete(picklistOrderItemDocument);
        }
    }

    @Override
    public void deleteAllDocumentFromEsForShipment(String shipmentId) {
        List<PicklistOrderItemDocument> shipments =
                picklistOrderItemDocumentRepository.findByShipmentId(shipmentId);
        if (!CollectionUtils.isEmpty(shipments)) {
            log.info("deleteAllDocumentFromEsForShipment {}",shipmentId);
            picklistOrderItemDocumentRepository.deleteAll(shipments);
        }
    }

    @Override
    public void deletePicklistDocumentFromEs(List<PickingDetail> pickingDetailList) {
        List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepo.findByWmsOrderItemIdInAndPickingSource(pickingDetailList.stream().map(PickingDetail :: getWmsOrderItemId).collect(Collectors.toList()), Constant.PICKING_CONSTANT.WMS_SOURCE);
        List<Long> picklistOrderItemIds = picklistOrderItemList.stream()
                .map(PicklistOrderItem::getId)
                .collect(Collectors.toList());

        List<PicklistOrderItemDocument> picklistOrderItemDocumentBatch = picklistOrderItemDocumentRepository.findByPicklistOrderItemIdIn(picklistOrderItemIds);
        if (!picklistOrderItemDocumentBatch.isEmpty()) {
            picklistOrderItemDocumentRepository.deleteAll(picklistOrderItemDocumentBatch);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateStatusForEsSyncAndPriority(List<Long> idList, int priority) {
        log.info("[updateStatusForEsSyncAndPriority] updating status for size : {}",idList.size());
        List<PicklistOrderItem> picklistOrderItems = picklistOrderItemRepo.findByIdIn(idList);
        List<PicklistOrderItem> picklistOrderItemList = new ArrayList<>();
        for(PicklistOrderItem picklistOrderItem : picklistOrderItems) {
            picklistOrderItem.setStatus(0);
            picklistOrderItem.setPriority(priority);
            picklistOrderItemList.add(picklistOrderItem);
        }
        picklistOrderItemRepo.saveAll(picklistOrderItemList);
        log.info("[updateStatusForEsSyncAndPriority] updated status for all items");
        return "Item status Successfully updated";
    }

    @Override
    public void syncPicklistOrderItemInESForManualTransfer(List<PicklistOrderItem> picklistOrderItems) {
        log.info("saving manual moved item to ES for size - {}",picklistOrderItems.size());
        try {
            List<PicklistOrderItemDocument> picklistOrderItemDocumentList = new ArrayList<>();
            List<PicklistOrderItem> esSyncPicklist = new ArrayList<>();
            for (PicklistOrderItem picklistOrderItem :
                    picklistOrderItems) {
                try {
                    updatePidLocation(null, false,
                            esSyncPicklist, picklistOrderItem);
                } catch (Exception e) {
                    log.error("Exception while updating location barcode - " + picklistOrderItem.getShipmentId() + " error message - " + e.getMessage());
                    throw new RuntimeException(e);
                }
                syncInESServiceImpl.savePicklistOrderItemInES(picklistOrderItem,
                        picklistOrderItemDocumentList);
            }
            log.info("Move to ES document list - {}", picklistOrderItemDocumentList.size());
            if (!CollectionUtils.isEmpty(picklistOrderItemDocumentList))
                picklistOrderItemDocumentRepository.saveAll(picklistOrderItemDocumentList);
            log.info("save successfully items to ES");
        } catch (Exception e) {
            log.error("Exception while pushing to ES error message - " + e.getMessage());
            e.printStackTrace();
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void asrsToManualWarehouse(AdverbToManualPojo adverbToManualPojo) {
        try {
            List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepo.findByShipmentId(adverbToManualPojo.getShipmentId());
            for (PicklistOrderItem picklistOrderItem : picklistOrderItemList) {
                PickingDetail pickingDetail = pickingDetailRepository.findByPicklistOrderItemId(picklistOrderItem.getId());
                boolean shouldAddToSyncList = false;
                List<PicklistOrderItem> esSyncItemList = new ArrayList<>();
                if (PickingCommonUtils.isDiscardToManualEligible(picklistOrderItem, pickingDetail, adverbToManualPojo.getOrderState())) {
                    picklistOrderItem.setOrderState(adverbToManualPojo.getOrderState().name());
                    picklistOrderItem.setUpdatedBy(adverbToManualPojo.getUserId());
                    picklistOrderItem.setStatus(Constant.PICKLIST_ORDER_STATUS.ES_SYNC);
                    pickingDetail.setStatus(adverbToManualPojo.getOrderState().name());
                    pickingDetail.setSkippedBy(adverbToManualPojo.getUserId());
                    pickingDetail.setUpdatedBy(adverbToManualPojo.getUserId());
                    shouldAddToSyncList = true;
                    pickingDetailRepository.save(pickingDetail);
                    picklistOrderItemRepo.save(picklistOrderItem);
                }

                if (shouldAddToSyncList) {
                    esSyncItemList.add(picklistOrderItem);
                }

                syncPicklistOrderItemInESForManualTransfer(esSyncItemList);
            }
        } catch (Exception e) {
            log.error("Exception occured while moving shipment to manual Wh : "+adverbToManualPojo.getShipmentId() + ", error message : "+e.getMessage());
        }
    }


    private void updatePickingDetails(PicklistOrderItem picklistOrderItemDetail,FulfillableType fulfillableType) {
        PickingDetail pickingDetail = pickingDetailRepository.findByPicklistOrderItemId(picklistOrderItemDetail.getId());
        if(null == pickingDetail)
            return;
        pickingDetail.setFullFillType(fulfillableType);
        pickingDetail = pickingDetailRepository.save(pickingDetail);
    }


    private boolean checkIfBulkOrder(OrderDetailsResponse orderDetailsResponse) {
        log.info("[PickListOrderItemServiceImpl, checkIfBulkOrder] Custom fields: {} for increment id: {}",
                orderDetailsResponse.getCustomFields(), orderDetailsResponse.getIncrementId());
        if (orderDetailsResponse.getCustomFields() == null ||
                CollectionUtils.isEmpty(orderDetailsResponse.getCustomFields().getCustomFields())) {
            return false;
        }

        if (isAutoReplenishemntOrder(orderDetailsResponse)) {
            return false;
        }

        Map<String, String> customFields = orderDetailsResponse.getCustomFields().getCustomFields();
        String isBulkOrder = customFields.get(Constant.PICKING_CONSTANT.IS_BULK_ORDER);
        if (isBulkOrder == null) {
            return false;
        }

        return Constant.JSON_CONSTANT.TRUE.equalsIgnoreCase(isBulkOrder);
    }

    private boolean isAutoReplenishemntOrder(OrderDetailsResponse orderDetailsResponse) {
        try {

            if (null != orderDetailsResponse && null != orderDetailsResponse.getOrderItemHeaderResponse() &&
                    null != orderDetailsResponse.getOrderItemHeaderResponse().getCustomFields() && !CollectionUtils.isEmpty(orderDetailsResponse.getOrderItemHeaderResponse().getCustomFields().getCustomFields())) {
                return orderDetailsResponse.getOrderItemHeaderResponse().getCustomFields().getCustomFields()
                        .containsKey(ORDER_PRIORITY_TYPE.name()) &&
                        AUTOREPLENISHMENT.equals(orderDetailsResponse.getOrderItemHeaderResponse().getCustomFields()
                                .getCustomFields().get(ORDER_PRIORITY_TYPE.name()));
            }
            log.info("[{}, isAutoReplenishemntOrder] Unable to determine auto replnishemt hence making it false");
            return false;
        }catch (Exception e) {
            log.error("[{}, isAutoReplenishemntOrder] Unable to determine auto replnishemt hence making it false due to exception {}",this.getClass().getSimpleName(), e);
            return false;
        }
    }


    private static String getPickingSource(OrderDetailsResponse orderDetailsResponse) {
        String pickingSource = null;
        if (Constant.PICKING_CONSTANT.TRANSFER_SOURCE.equalsIgnoreCase(orderDetailsResponse.getPickingSource())) {
            pickingSource = Constant.PICKING_CONSTANT.TRANSFER_SOURCE;
        }
        else {
            pickingSource = Constant.PICKING_CONSTANT.WMS_SOURCE;
        }
        return pickingSource;
    }

    private PicklistOrderItem createPicklistOrderItem(OrderItemResponse orderItemResponse,
                                                      OrderDetailsResponse orderDetailsResponse, Integer noOfProduct,
                                                      String rePickStatus, boolean isJIT, String jitType,
                                                      String eventIdempotencyKey, String pickingSource,
                                                      boolean isBulkOrder, String fr1OrFr2, String loyaltyTier)throws Exception {
        PicklistOrderItem picklistOrderItem = new PicklistOrderItem();
        log.info("[createPicklistOrderItem] Save Detail in Picklist Order Item from WMS ,shipment id :{}, wms order item id:{}",
                orderItemResponse.getShippingPackageId(), orderItemResponse.getId().intValue());

        picklistOrderItem.setIncrementId(orderDetailsResponse.getIncrementId());
        picklistOrderItem.setProductId(orderItemResponse.getProduct_id());
        picklistOrderItem.setWmsOrderId(orderDetailsResponse.getId().intValue());
        picklistOrderItem.setWmsOrderItemId(orderItemResponse.getId().intValue());
        picklistOrderItem.setWmsOrderCode(orderItemResponse.getWmsOrderCode());
        picklistOrderItem.setShipmentId(orderItemResponse.getShippingPackageId());
        picklistOrderItem.setScmOrderId(orderDetailsResponse.getOrderId());
        picklistOrderItem.setScmOrderItemId(orderItemResponse.getOrderItemId());
        picklistOrderItem.setNoItemPerOrder(orderDetailsResponse.getNoOfOrderItem());
        picklistOrderItem.setNoProduct(noOfProduct);
        picklistOrderItem.setOrderState(orderDetailsResponse.getStatus());
        picklistOrderItem.setJitOrder(JITType.valueOf(jitType));
        picklistOrderItem.setPincode(orderDetailsResponse.getOrderItemHeaderResponse().getPincode());
        picklistOrderItem.setChannel(orderItemResponse.getChannel());

        String orderType = orderDetailsResponse.getOrderType();
        if (isSuperOrder(orderItemResponse)) {
            orderType = orderType.equals(DISTRIBUTED_ORDERS) ? DO_SO_ORDERS : PickingConsumerConstant.SUPER_ORDER_TYPE;
        }
        if(DO_JIT_CHANNEL.equalsIgnoreCase(orderItemResponse.getChannel())) {
            orderType = DISTRIBUTED_ORDERS;
        }

        picklistOrderItem.setOrderType(StringUtils.isEmpty(orderType)?"":orderType.toUpperCase());
        picklistOrderItem.setFacility(orderItemResponse.getFacilityCode());
        picklistOrderItem.setScmOrderCreatedAt(orderDetailsResponse.getOrderCreatedAt());

        picklistOrderItem.setFitting(isFittingRequired(orderItemResponse));
        if (orderItemResponse.getFittingId() != null)
            picklistOrderItem.setFittingId(orderItemResponse.getFittingId());

        if (allowedFacilitiesForLensFr1OrFr2.contains(orderItemResponse.getFacilityCode()) && fr1OrFr2 != null) {
            picklistOrderItem.setProcessingType(fr1OrFr2);
        } else if (orderItemResponse.getProcessingType() != null) {
            picklistOrderItem.setProcessingType(orderItemResponse.getProcessingType().name());
        }
        if (orderItemResponse.getItemType() != null)
            picklistOrderItem.setItemType(orderItemResponse.getItemType().name());

        Map<String, String> customFields = orderItemResponse.getCustomFields() == null ? null : orderItemResponse.getCustomFields().getCustomFields();
            // for now we will set fast picking as false.
//                    if (customFields.containsKey("FAST_PICKING"))
//                        picklistOrderItem.setFastPicking(Boolean.parseBoolean(customFields.get("FAST_PICKING")));
        picklistOrderItem.setFastPicking(isPidEligibleForFastPicking(orderItemResponse.getProduct_id(), orderItemResponse.getFacilityCode()));

        picklistOrderItem.setCreatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
        picklistOrderItem.setCreatedAt(new Date());
        picklistOrderItem.setRepickStatus(null);
        picklistOrderItem.setRepickCount(0);


        picklistOrderItem.setPickingSource(pickingSource);

        updateLensDetails(picklistOrderItem, orderItemResponse, rePickStatus);
        picklistOrderItem.setEventIdempotencyKey(eventIdempotencyKey);
        picklistOrderItem.setBulkOrder(isBulkOrder);
        picklistOrderItem.setLegalOwner(orderDetailsResponse.getOrderItemHeaderResponse().getLegalOwner());
        picklistOrderItem.setFullFillType(orderItemResponse.getFulfillableType());
        setPickingPriorityAndPickingCutOffTime(orderDetailsResponse, picklistOrderItem, isBulkOrder, rePickStatus,loyaltyTier);

        if(customFields!=null && customFields.containsKey("STORE_INVENTORY_TYPE")) {
            String storeInventoryType = customFields.get("STORE_INVENTORY_TYPE");
            if ((alterPriorityForLOLPFacilityList.contains(orderItemResponse.getFacilityCode())) && ("LO".equalsIgnoreCase(storeInventoryType)||"LP".equalsIgnoreCase(storeInventoryType)||"TLP".equalsIgnoreCase(storeInventoryType))) {
                picklistOrderItem.setPriority(15);
            }
        }
        PicklistOrderItem picklistOrderItemDetail = picklistOrderItemRepo.save(picklistOrderItem);

        List<PicklistOrderItemMetaData> picklistOrderItemMetaDataList = new ArrayList<>();
        PicklistOrderItemMetaData picklistOrderItemMetaData = new PicklistOrderItemMetaData();
        picklistOrderItemMetaData.setPicklistOrderItemId(picklistOrderItemDetail);
        picklistOrderItemMetaData.setPairValue(orderDetailsResponse.getDeliveryCountryCode());
        picklistOrderItemMetaData.setPairKey(Constant.PICKLIST_ORDER_META.COUNRY_CODE);
        picklistOrderItemMetaData.setCreatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
        picklistOrderItemMetaData.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
        picklistOrderItemMetaData = picklistOrderItemMetaDataRepository.save(picklistOrderItemMetaData);
        picklistOrderItemMetaDataList.add(picklistOrderItemMetaData);

        //TODo: CleanUp: Move picklistOrderItemMEtaData setting to a util class like done with showing qc fail count
        // Also use the same method to update TL LP or LO dont need seperated methods or pair_key
        checkAndUpdateStoreInventoryType(customFields, picklistOrderItemMetaDataList, picklistOrderItemDetail);
        checkAndUpdateLO(customFields, picklistOrderItemMetaDataList, picklistOrderItemDetail);


        picklistOrderItemDetail.setPicklistOrderItemMetaDataList(picklistOrderItemMetaDataList);
        log.info("[PicklistOrderItem] saved in db with values {} | {}", picklistOrderItemDetail,
                picklistOrderItemMetaDataList.size());
        return picklistOrderItemDetail;
    }

    private void checkAndUpdateStoreInventoryType(Map<String, String> customFields, List<PicklistOrderItemMetaData> picklistOrderItemMetaDataList, PicklistOrderItem picklistOrderItemDetail) {
        if(customFields!=null && customFields.containsKey(Constant.PICKLIST_ORDER_META.STORE_INVENTORY_TYPE)) {
            String storeInventoryType = customFields.get(Constant.PICKLIST_ORDER_META.STORE_INVENTORY_TYPE);
            if(Constant.PICKLIST_ORDER_META.LP.equalsIgnoreCase(storeInventoryType) &&
                    (customFields.containsKey(Constant.PICKLIST_ORDER_META.FULFILLABLE_TYPE) && FulfillableType.NON_FULFILLABLE.name().equals(customFields.get(Constant.PICKLIST_ORDER_META.FULFILLABLE_TYPE)))) {
                log.info("[PicklistOrderItemServiceImpl, checkAndUpdateStoreInventoryType] Updating storeInventoryType to TLP for LP order for picklistOrderItemID {}",picklistOrderItemDetail.getId());
                storeInventoryType = Constant.PICKLIST_ORDER_META.TLP;
            }
            if(Constant.PICKLIST_ORDER_META.LP.equalsIgnoreCase(storeInventoryType) || Constant.PICKLIST_ORDER_META.TLP.equalsIgnoreCase(storeInventoryType)) {
                PicklistOrderItemMetaData picklistOrderItemMetaData = new PicklistOrderItemMetaData();
                picklistOrderItemMetaData.setPicklistOrderItemId(picklistOrderItemDetail);
                picklistOrderItemMetaData.setPairValue(storeInventoryType);
                picklistOrderItemMetaData.setPairKey(Constant.PICKLIST_ORDER_META.TLP_UNFF_ORDER);
                picklistOrderItemMetaData.setCreatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
                picklistOrderItemMetaData.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
                picklistOrderItemMetaData = picklistOrderItemMetaDataRepository.save(picklistOrderItemMetaData);
                picklistOrderItemMetaDataList.add(picklistOrderItemMetaData);
            }
        }
    }

    private void checkAndUpdateLO(Map<String, String> customFields, List<PicklistOrderItemMetaData> picklistOrderItemMetaDataList, PicklistOrderItem picklistOrderItemDetail) {
        if(customFields!=null && customFields.containsKey(Constant.PICKLIST_ORDER_META.STORE_INVENTORY_TYPE)) {
            String storeInventoryType = customFields.get(Constant.PICKLIST_ORDER_META.STORE_INVENTORY_TYPE);
            if(Constant.PICKLIST_ORDER_META.LO.equalsIgnoreCase(storeInventoryType)) {
                PicklistOrderItemMetaData picklistOrderItemMetaData = new PicklistOrderItemMetaData();
                picklistOrderItemMetaData.setPicklistOrderItemId(picklistOrderItemDetail);
                picklistOrderItemMetaData.setPairValue(storeInventoryType);
                picklistOrderItemMetaData.setPairKey(Constant.PICKLIST_ORDER_META.LO_ORDER);
                picklistOrderItemMetaData.setCreatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
                picklistOrderItemMetaData.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
                picklistOrderItemMetaData = picklistOrderItemMetaDataRepository.save(picklistOrderItemMetaData);
                picklistOrderItemMetaDataList.add(picklistOrderItemMetaData);
            }
        }
    }

    private void setPickingPriorityAndPickingCutOffTime(OrderDetailsResponse orderDetailsResponse, PicklistOrderItem picklistOrderItem, boolean isBulkOrder, String repickStatus, String loyaltyTier) throws Exception {

        if(pickingPriorityCutoffConfigurationEnabled){
            int priority;
            if(enableWmsPriority)
                priority = orderDetailsResponse.getOrderItemHeaderResponse().getOrderPriority() != null ? orderDetailsResponse.getOrderItemHeaderResponse().getOrderPriority() : PickingConsumerConstant.DEFAULT_PINCODE_PRIORITY;
            else
                priority = getPriorityUsingRepickStatusAndPincode(orderDetailsResponse.getOrderItemHeaderResponse().getPincode(), repickStatus, orderDetailsResponse.getOrderCreatedAt(),isBulkOrder);
            Actions actions = setPickingPriorityAndPickingCutOffTimeBasedOnConfiguration(orderDetailsResponse, picklistOrderItem, priority, repickStatus,loyaltyTier);
            if(null == actions) {
                priority = getPriorityUsingRepickStatusAndOrderPriority(orderDetailsResponse, repickStatus, isBulkOrder, picklistOrderItem);
                setPriortiyAndCutOffUsingOldLogic(orderDetailsResponse, picklistOrderItem, priority,repickStatus,loyaltyTier);
            }
        } else {
            int priority = getPriorityUsingRepickStatusAndOrderPriority(orderDetailsResponse, repickStatus, isBulkOrder, picklistOrderItem);
            setPriortiyAndCutOffUsingOldLogic(orderDetailsResponse, picklistOrderItem, priority,repickStatus,loyaltyTier);
        }
    }

    private void setPriortiyAndCutOffUsingOldLogic(OrderDetailsResponse orderDetailsResponse, PicklistOrderItem picklistOrderItem, int priority, String repickStatus, String loyaltyTier) {
        picklistOrderItem.setPriority(priority);
        log.info("[createPicklistOrderItem] current priority for shipment_id :{}  is :{} ", picklistOrderItem.getShipmentId(), picklistOrderItem.getPriority());
        if (picklistOrderItem.getPickingCutoff() == null || DBEnums.REPICK_QC_FAIL.equalsIgnoreCase(repickStatus)) {
            Date cutOffTime = fetchCutOffTimeBasedOnPriority(new Date(), priority, loyaltyTier);
            log.info("[PickListOrderITemServiceImpl, createPicklistOrderItem] The cutOffTime for picklistORderItem with Id {} and priority {} is {}", picklistOrderItem.getId(), priority, cutOffTime);
            picklistOrderItem.setPickingCutoff(cutOffTime);
        }
    }

    private Actions setPickingPriorityAndPickingCutOffTimeBasedOnConfiguration(
            OrderDetailsResponse orderDetailsResponse,
            PicklistOrderItem picklistOrderItem,
            Integer priority,
            String repickStatus,String loyaltyTier) throws Exception {


        try {
            // Prepare context for rule engine
            Map<String, Object> context = new HashMap<>();
            ConditionDto conditionDto = buildConditionDto(orderDetailsResponse, priority, repickStatus, loyaltyTier);
            context.put(PickingConsumerConstant.RULE_BASED_PRIORITY_CONSTANTS.CONDITION_DTO, conditionDto);
            log.info("[{}, setPickingPriorityAndPickingCutOffTimeBasedOnConfiguration] The condition formed for picklistOrderItemId {} is {}", this.getClass().getSimpleName(),
                    picklistOrderItem.getId(), conditionDto);
            // Execute rules using the rule engine
            Actions actions = ruleEngineService.executeRules(context);
            log.info("[{}, setPickingPriorityAndPickingCutOffTimeBasedOnConfiguration] The rule formed for picklistOrderItemId {} is {}", this.getClass().getSimpleName(),
                    picklistOrderItem.getId(), actions);
            // Set priority and cutoff time with default fallback for null actions
            if (actions != null) {
                picklistOrderItem.setPriority(actions.getPickingPriority());
                Date cutoffDate = getCutoffDateBasedOnConfiguration(actions);
                picklistOrderItem.setPickingCutoff(cutoffDate);
            }
            return actions;
        }catch (Exception e) {
            log.error("[{}, setPickingPriorityAndPickingCutOffTimeBasedOnConfiguration] Unable to create picking cutoff using rule base config hence using the old way. The exception is {}",this.getClass().getSimpleName(),e);
            return null;
        }
    }

    private ConditionDto buildConditionDto(OrderDetailsResponse orderDetailsResponse, Integer priority, String repickStatus, String loyaltyTier) throws Exception {
        ConditionDto conditionDto = new ConditionDto();
        // Set pincode classification based on priority
        if(null == priority)
            priority = -1;
        conditionDto.setPincodeClassification(Integer.valueOf(PINCODE_PRIORITY).equals(priority) ?
                PickingConsumerConstant.RULE_BASED_PRIORITY_CONSTANTS.NDD : priority.toString());
        conditionDto.setOrderPriority(priority);

        // Fetch and set membership (loyalty tier)

        conditionDto.setMembership(loyaltyTier);

        // Set order type based on repick status
        conditionDto.setOrderType( null == repickStatus ? PickingConsumerConstant.RULE_BASED_PRIORITY_CONSTANTS.NEW : repickStatus);
        conditionDto.setFacility(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().get(0).getFacilityCode());
        // Set sync start and end time based on order creation time
        Date orderCreatedAt =  NexsDateUtil.convertGMTToISTHoursAndMinuteFormatter(orderDetailsResponse.getOrderCreatedAt());;
        Integer syncTime = NexsDateUtil.extractHoursOfDayAsIst(orderCreatedAt);
        conditionDto.setOrderSyncStartTime(syncTime);
        conditionDto.setOrderSyncEndTime(syncTime);
        conditionDto.setSource(orderDetailsResponse.getSource());

        return conditionDto;
    }

    private void setDefaultPickingPriorityAndCutoffTime(PicklistOrderItem picklistOrderItem) {
        picklistOrderItem.setPriority(10);
        picklistOrderItem.setPickingCutoff(NexsDateUtil.createCutOffTimeForFixedType(new Date(), 720,totalPickingSlots));
    }


    private Date getCutoffDateBasedOnConfiguration(Actions actions) {
        if(PickingConsumerConstant.RULE_BASED_PRIORITY_CONSTANTS.INCREMENTAL.equals(actions.getPickingCutoffType())) {
            return NexsDateUtil.createCutOffTimeForIncrementalType(new Date(), actions.getPickingCutoffInMinute());
        } else if (PickingConsumerConstant.RULE_BASED_PRIORITY_CONSTANTS.FIXED.equals(actions.getPickingCutoffType())) {
            return NexsDateUtil.createCutOffTimeForFixedType(new Date(), actions.getPickingCutoffInMinute(),totalPickingSlots);
        } else {
            return NexsDateUtil.createCutOffTimeForFixedType(new Date(), actions.getPickingCutoffInMinute(),totalPickingSlots);
        }
    }

    private boolean isSuperOrder(OrderItemResponse orderItemResponse) {
        boolean isSuperOrder = orderItemResponse.getCustomFields() != null && orderItemResponse.getCustomFields() != null &&
                orderItemResponse.getCustomFields().getCustomFields() != null &&
                orderItemResponse.getCustomFields().getCustomFields().containsKey(PickingConsumerConstant.SUPER_ORDER_TYPE) &&
                "true".equals(orderItemResponse.getCustomFields().getCustomFields().get(PickingConsumerConstant.SUPER_ORDER_TYPE));
        log.info("[createPicklistOrderItem] Shipment id :{}, wms order item id: {}, orderType {}",
                orderItemResponse.getShippingPackageId(), orderItemResponse.getId().intValue(), isSuperOrder);
        return isSuperOrder;
    }

    private Date fetchCutOffTimeBasedOnPriority(Date date, int priority, String loyaltyTier) {
        Date cutOffTime = null;
        log.info("[WavePayloadUtil, fetchCutOffTimeBasedOnPriority] The priority list for which cutoff is allowed is {} and is cuttOff changed allowed on priorit : {}",alterCutOffPriorityList,allowCutOffOnPriority);
        if(allowCutOffOnPriority && alterCutOffPriorityList.contains(priority)) {
            if(isGoldMaxCutOffAlterAllowed) {
                if(Constant.PICKING_CONSTANT.LOYALTY_TYPES.GOLD_MAX.equalsIgnoreCase(loyaltyTier)) {
                    cutOffTime = date != null ? addverbUtils.createAddverbCutOffTimeForNDDGoldMax(date) : null;
                } else if (null!=loyaltyTier && loyaltyTier.contains(Constant.PICKING_CONSTANT.LOYALTY_TYPES.GOLD)) {
                    cutOffTime = date != null ? addverbUtils.createAddverbCutOffTimeForNDDGold(date) : null;
                }
                else {
                    cutOffTime = date != null ? addverbUtils.createAddverbCutOffTimeForNDD(date) : null;
                }
            }else {
                cutOffTime = date != null ? addverbUtils.createAddverbCutOffTime(date) : null;
            }
        }
        else if(allowCutOffOnPriority && alterCutOffPriorityP11List.contains(priority)) {
            cutOffTime = date!=null?addverbUtils.createAddverbCutOffTimeForP11(date):null;
        }
        else {
            cutOffTime = findPickingCutOff(date);
        }
        return cutOffTime;
    }

    public Date findPickingCutOff(Date date) {
        Date pickingCutOff = date;
        int alterPickingCutOffByMinutes = pickingConfig.getAlterPickingCutOffByMinutes();
        try {
            log.info("[PicklistOrderItemServiceImpl, findPickingCutOff]: The number of slots to be divided are {}", totalPickingSlots);
            //Find the cutOff points of hours
           pickingCutOff = NexsDateUtil.createCutOffTimeForFixedType(date,alterPickingCutOffByMinutes,totalPickingSlots);
            log.info("[PickListOrderItemServiceImpl, findPickingCutOff]] The picking cutOff formed is {} after adding additional minutes {}",pickingCutOff,alterPickingCutOffByMinutes);

        }catch (Exception e) { //Incase of failure we will revert back to setting cutOff as 3 hour additional to the current time
            log.info("[PickListOrderItemServiceImpl, findPickingCutOff] Setting picking CUtOff to current date because we are unable to find the pickingCutOff due to error {}",e);
            pickingCutOff = DateUtils.addHours(date,Constant.ADDVERB_CONSTANT.DEFAULT_CUTOFF_HOURS);
        }
        log.info("[PickListOrderItemServiceImpl, findPickingCutOff]: The picking cutOff is {}",pickingCutOff.toString());
        return pickingCutOff;
    }

    public int getPriorityUsingRepickStatusAndPincode(String pincode, String repickStatus, Date createdAt, boolean isBulkOrder)throws Exception{
        log.info("fetching priority for the pincode :{}", pincode);
        log.info("[PicklistOrderItemServiceImpl, getPriorityUsingRepickStatusAndPincode] The repick status is {}",repickStatus);
        int priority = PickingConsumerConstant.DEFAULT_PINCODE_PRIORITY;
        List<String> priorityPincodes = getPriorityPincodes();
        if( DBEnums.REPICK_QC_FAIL.equalsIgnoreCase(repickStatus) && !priorityPincodes.contains(pincode)) {
            log.info("[{}, getPriorityUsingRepickStatusAndPincode] The repickStatus is {} hence setting priority as 0",CLASS_NAME,repickStatus);
            priority = Constant.PICKING_DEFAULT.QC_FAILED_PRIORITY;
        }
        else if (priorityPincodes.contains(pincode)){
            if(setPriorityOnCreatedAtTime) {
                //Note: The time here is in GMT so please be careful
                priority = checkTimeBasedPriorityForOrder(createdAt);
            }else {
                log.info("[{}, getPriorityUsingRepickStatusAndPincode]pincode found in the priority list, setting priority to one for pincode :{}", CLASS_NAME, pincode);
                priority = PINCODE_PRIORITY;
            }
        }
        log.info("[{} , getPriorityUsingRepickStatusAndPincode] current priority is :{} ",CLASS_NAME, priority);
        return priority;
    }

    private int getPriorityUsingRepickStatusAndOrderPriority(OrderDetailsResponse orderDetailsResponse, String repickStatus, boolean isBulkOrder, PicklistOrderItem picklistOrderItem) throws Exception {
        OrderItemHeaderResponse orderItemHeaderResponse = orderDetailsResponse.getOrderItemHeaderResponse();
        if(!enableWmsPriority) {
            log.info("wms order priority flag disabled, moving via older flow for id - {}",orderDetailsResponse.getIncrementId());
            return getPriorityUsingRepickStatusAndPincode(orderItemHeaderResponse.getPincode(), repickStatus, orderDetailsResponse.getOrderCreatedAt(),isBulkOrder);
        }
        log.info("order priority fetched for {} with priority: {}", orderDetailsResponse.getIncrementId(), orderItemHeaderResponse.getOrderPriority());
        log.info("[PicklistOrderItemServiceImpl, getPriorityUsingRepickStatusAndOrderPriority] The repick status is {}",repickStatus);
        int priority = orderItemHeaderResponse.getOrderPriority() != null ? orderItemHeaderResponse.getOrderPriority() : PickingConsumerConstant.DEFAULT_PINCODE_PRIORITY;
        if(!StringUtils.isEmpty(repickStatus) && DBEnums.REPICK_QC_FAIL.equalsIgnoreCase(repickStatus) && (allowedPrioritiesForQcFail.contains(priority) || allowedFacilitiesForQcFailPriorityZero.contains(picklistOrderItem.getFacility()))) {
            log.info("[{}, getPriorityUsingRepickStatusAndOrderPriority] The repickStatus is {} hence setting priority as 0",CLASS_NAME,repickStatus);
            return  Constant.PICKING_DEFAULT.QC_FAILED_PRIORITY;
        }
        if(priority == PINCODE_PRIORITY && setPriorityOnCreatedAtTime) {
            priority = checkTimeBasedPriorityForOrder(orderDetailsResponse.getOrderCreatedAt());
        }

        if(priority == PINCODE_PRIORITY  && setPriorityOnCitywiseCuttOff) {
            return checkCourierCutOffTimeBasedPriorityForOrder(orderDetailsResponse, picklistOrderItem,priority);
        }
        return priority;
    }

    private int checkTimeBasedPriorityForOrder(Date createdAt) throws Exception {
        Date createdAtIst = NexsDateUtil.convertGMTToISTHoursAndMinuteFormatter(createdAt);
        log.info("[{}, getPriorityUsingRepickStatusAndPincode] The createdAt is {} and in IST is {}",this.getClass().getSimpleName(),createdAt,createdAtIst);
        Date createAtHoursAndMinutes = NexsDateUtil.extractHoursAndMinutesOfDayAsIst(createdAtIst);
        Date pOneOrdersStartHoursAndMinutes = NexsDateUtil.extractHoursAndMinutesOfDayAsIst(NexsDateUtil.convertStringToHoursAndMinutes(pOneOrdersStartTime));
        Date pOneOrdersEndHoursAndMinutes = NexsDateUtil.extractHoursAndMinutesOfDayAsIst(NexsDateUtil.convertStringToHoursAndMinutes(pOneOrdersEndTime));
        if(createAtHoursAndMinutes.compareTo(pOneOrdersStartHoursAndMinutes)>=0 && createAtHoursAndMinutes.compareTo(pOneOrdersEndHoursAndMinutes)<=0) {
            return PINCODE_PRIORITY;
        }
        else {
            return Constant.PICKING_DEFAULT.SECONDARY_PINCODE_PRIORITY;
        }
    }

    private int checkCourierCutOffTimeBasedPriorityForOrder(OrderDetailsResponse orderDetailsResponse, PicklistOrderItem picklistOrderItem, Integer priority) throws Exception {
        log.info("[checkCourierCutOffTimeBasedPriorityForOrder] cheking city wise cutoff inc id - > {}",orderDetailsResponse.getIncrementId());
        String pincode = orderDetailsResponse.getOrderItemHeaderResponse().getPincode();

        if(Constant.PICKING_CONSTANT.B2B_ORDER.equalsIgnoreCase(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().get(0).getProductDeliveryType())) {
            Map<String, String> customFields = orderDetailsResponse.getCustomFields().getCustomFields();
            if(customFields!=null && customFields.containsKey(Constant.PICKING_CONSTANT.SHIPPING_PINCODE)) {
                pincode = customFields.get(Constant.PICKING_CONSTANT.SHIPPING_PINCODE);
            }
        }

        log.info("[checkCourierCutOffTimeBasedPriorityForOrder] pincode = {}",pincode);
        CourierCutoffDetail courierCutoffDetail = courierCutoffDetailRepository.findTopByPincodeAndFacilityOrderByIDDesc(pincode, orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().get(0).getFacilityCode()).orElse(null);
        log.info("[checkCourierCutOffTimeBasedPriorityForOrder] courierCutoffDetail fetched for pincopde - {}, cutoff time - {}, for facility - {}",pincode, courierCutoffDetail, orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().get(0).getFacilityCode());
        if(courierCutoffDetail != null) {
            Date createdAtIst = NexsDateUtil.convertGMTToISTHoursAndMinuteFormatter(orderDetailsResponse.getOrderCreatedAt());
            LocalTime orderTime = createdAtIst.toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
            log.info("[checkCourierCutOffTimeBasedPriorityForOrder] Order time in localdate - {}",orderTime);
            LocalTime adjustedCutoffTime = courierCutoffDetail.getLastCourierCutoff().minusMinutes(orderProcessingTimeBuffer);
            log.info("[checkCourierCutOffTimeBasedPriorityForOrder] adjustedCutoffTime time in localdate - {}", adjustedCutoffTime);
            String loyaltyTier = null;
            if(isGoldMaxCutOffAlterAllowed && alterCutOffPriorityList.contains(priority)) {
                loyaltyTier = pickingFetchAdditionalDataService.findLoyaltyTierUsingCustomerId(orderDetailsResponse.getCustomerId());
                int differenceInMinutes = nddOrderProcessingTimeBuffer;
                if(Constant.PICKING_CONSTANT.LOYALTY_TYPES.GOLD_MAX.equalsIgnoreCase(loyaltyTier)) {
                    differenceInMinutes = nddGoldMaxOrderProcessingTimeBuffer;
                } else if (null!=loyaltyTier && loyaltyTier.contains(Constant.PICKING_CONSTANT.LOYALTY_TYPES.GOLD)) {
                    differenceInMinutes = nddGoldOrderProcessingTimeBuffer;
                }
                adjustedCutoffTime = courierCutoffDetail.getLastCourierCutoff().minusMinutes(differenceInMinutes);
            }
            if(orderTime.isBefore(adjustedCutoffTime)) {
                // Convert adjustedCutoffTime (LocalTime) to Date and setting as picking cutoff
                LocalDateTime adjustedCutoffDateTime = LocalDateTime.of(LocalDate.now(), adjustedCutoffTime);
                ZonedDateTime istZonedDateTime = adjustedCutoffDateTime.atZone(ZoneId.of("Asia/Kolkata"));
                ZonedDateTime gmtZonedDateTime = istZonedDateTime.withZoneSameInstant(ZoneId.of("GMT"));
                Date adjustedCutoffDate = Date.from(gmtZonedDateTime.toInstant());
                picklistOrderItem.setPickingCutoff(adjustedCutoffDate);
                log.info("[checkCourierCutOffTimeBasedPriorityForOrder, createPicklistOrderItem] The cutOffTime for picklistORderItem with Id {} and priority {} is {}", picklistOrderItem.getId(), 1, adjustedCutoffDateTime);
            }
        }
        return PINCODE_PRIORITY;
    }

    private List<String> getPriorityPincodes() throws Exception {
        List<String> priorityPincodes = dbConfig.getPriorityPincodes();
        log.info("[{} , getPriorityPincodes] The priority pincode list fetched  is {}",CLASS_NAME,priorityPincodes);
        if(CollectionUtils.isEmpty(priorityPincodes)) {
            log.info("[{} , getPriorityPincodes] The priority pincode list fetched  is {} and is not allowed hence updating slack and retrying",CLASS_NAME,priorityPincodes);
            slackMessageSender.sendMessageToChannelViaApp(pickingAlertSlackChannel,"Unable to fetch priority pincde list at time `"+ new Date() + "` hence retrying please keep a watch on order sync and priority");
            dbConfig.reinitializeConfig();
            priorityPincodes = dbConfig.getPriorityPincodes();
            if(CollectionUtils.isEmpty(priorityPincodes)) {
                log.info("[{} , getPriorityPincodes] The priority pincode retry list fetched is {} hence throwing error",CLASS_NAME,priorityPincodes);
                throw new PickingException("The priority pincode retry list fetched is "+priorityPincodes+" hence throwing error",PickingExceptionStatus.INTERNAL_SERVER_ERROR);
            }
        }
        return priorityPincodes;
    }


    private PicklistOrderItem updatePicklistOrderItem(PicklistOrderItem picklistOrderItem, OrderItemResponse orderItemResponse,
                                                      OrderDetailsResponse orderDetailsResponse, String rePickStatus, String eventIdempotencyKey,
                                                      boolean isBulkOrder, boolean isFullfillableUpdate,
                                                      String loyaltyTier, String jitType,String fr1OrFr2) throws Exception {
        log.info("Update Picklist Order Item {}, {}, {}",
                picklistOrderItem.getId(), picklistOrderItem, rePickStatus);
        picklistOrderItem.setEsSyncLog(null);
        picklistOrderItem.setStatus(0);
        picklistOrderItem.setRepickStatus(rePickStatus);
        picklistOrderItem.setRepickCount(picklistOrderItem.getRepickCount() + 1);
        if(
                !isFullfillableUpdate ||
                picklistOrderItem.getNoItemPerOrder().equals(0)
        ) {
            picklistOrderItem.setNoItemPerOrder(orderDetailsResponse.getNoOfOrderItem());
        }
        picklistOrderItem.setFacility(orderItemResponse.getFacilityCode());
        picklistOrderItem.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_REPICKING_CALL);
        picklistOrderItem.setUpdatedAt(new Date());
        if(isIMSDuplicateCallAllowed) {
            String locationBarcode = getAsrsLocationBarcode(picklistOrderItem, false);
            String asrsLocationBarcode = getAsrsLocationBarcode(picklistOrderItem, true);
            picklistOrderItem.setLocationBarcode(locationBarcode);
            picklistOrderItem.setAsrsLocationBarcode(asrsLocationBarcode);
        }
        picklistOrderItem.setProductId(orderItemResponse.getProduct_id());
        picklistOrderItem.setShipmentId(orderItemResponse.getShippingPackageId());
        picklistOrderItem.setFittingId(orderItemResponse.getFittingId());
        updateLensDetails(picklistOrderItem, orderItemResponse, rePickStatus);
        picklistOrderItem.setEventIdempotencyKey(eventIdempotencyKey);
        if(!StringUtils.isEmpty(orderDetailsResponse.getOrderItemHeaderResponse().getPincode())){
        picklistOrderItem.setPincode(orderDetailsResponse.getOrderItemHeaderResponse().getPincode());
        }
        picklistOrderItem.setBulkOrder(isBulkOrder);
        picklistOrderItem.setFullFillType(orderItemResponse.getFulfillableType());
        picklistOrderItem.setJitOrder(JITType.valueOf(jitType));
        picklistOrderItem.setFastPicking(isPidEligibleForFastPicking(orderItemResponse.getProduct_id(),orderItemResponse.getFacilityCode()));
        if(DBEnums.REPICK_QC_FAIL.equalsIgnoreCase(rePickStatus) || "REASSIGNED".equalsIgnoreCase(picklistOrderItem.getOrderState()))
            setPickingPriorityAndPickingCutOffTime(orderDetailsResponse, picklistOrderItem, isBulkOrder, rePickStatus,loyaltyTier);
        picklistOrderItem.setOrderState(orderDetailsResponse.getStatus());
        picklistOrderItem.setWmsOrderCode(orderItemResponse.getWmsOrderCode());
        String orderType = orderDetailsResponse.getOrderType();
        if (isSuperOrder(orderItemResponse)) {
            orderType = orderType.equals(DISTRIBUTED_ORDERS) ? DO_SO_ORDERS : PickingConsumerConstant.SUPER_ORDER_TYPE;
        }
        if(DO_JIT_CHANNEL.equalsIgnoreCase(orderItemResponse.getChannel())) {
            orderType = DISTRIBUTED_ORDERS;
        }
        picklistOrderItem.setOrderType(StringUtils.isEmpty(orderType)?"":orderType.toUpperCase());

        if(holdStatusAllowedList.contains(picklistOrderItem.getStatus())) {
            picklistOrderItem.setOrderState(orderItemResponse.getHolded()==1?HOLDED:picklistOrderItem.getOrderState());
        }

        Optional<PicklistOrderItemMetaData> optionalPicklistOrderItemMetaData = picklistOrderItem.getPicklistOrderItemMetaDataList() != null
                ? picklistOrderItem.getPicklistOrderItemMetaDataList().stream()
                .filter(item -> Constant.PICKLIST_ORDER_META.COUNRY_CODE.equalsIgnoreCase(item.getPairKey()))
                .findFirst()
                : Optional.empty();

        PicklistOrderItemMetaData picklistOrderItemMetaData;
        if (optionalPicklistOrderItemMetaData.isPresent()) {
            picklistOrderItemMetaData = optionalPicklistOrderItemMetaData.get();
            picklistOrderItemMetaData.setPairValue(orderDetailsResponse.getDeliveryCountryCode());
            picklistOrderItemMetaData.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_REPICKING_CALL);
            picklistOrderItemMetaData = picklistOrderItemMetaDataRepository.save(picklistOrderItemMetaData);
        } else {
            // Initialize the list if it's null
            if (picklistOrderItem.getPicklistOrderItemMetaDataList() == null) {
                picklistOrderItem.setPicklistOrderItemMetaDataList(new ArrayList<>());
            }

            // Create and add new metadata
            picklistOrderItemMetaData = new PicklistOrderItemMetaData();
            picklistOrderItemMetaData.setPicklistOrderItemId(picklistOrderItem); // Set the relationship
            picklistOrderItemMetaData.setPairKey(Constant.PICKLIST_ORDER_META.COUNRY_CODE);
            picklistOrderItemMetaData.setPairValue(orderDetailsResponse.getDeliveryCountryCode());
            picklistOrderItemMetaData.setCreatedBy(Constant.PICKING_CONSTANT.DEFAULT_REPICKING_CALL);
            picklistOrderItemMetaData.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_REPICKING_CALL);
            picklistOrderItemMetaData = picklistOrderItemMetaDataRepository.save(picklistOrderItemMetaData);
            picklistOrderItem.getPicklistOrderItemMetaDataList().add(picklistOrderItemMetaData);
        }
//        if (allowedFacilitiesForLensFr1OrFr2.contains(orderItemResponse.getFacilityCode()) && fr1OrFr2 != null) {
//            picklistOrderItem.setProcessingType(fr1OrFr2);
//        } else if (orderItemResponse.getProcessingType() != null) {
//            picklistOrderItem.setProcessingType(orderItemResponse.getProcessingType().name());
//        }
        picklistOrderItem = picklistOrderItemRepo.save(picklistOrderItem);

        return picklistOrderItem;
    }


    /**
     * @param productId, facilityCode
     * @return
     * Method to check if the producId and the facility code belong to the list of fast picking
     */
    public boolean isPidEligibleForFastPicking(Integer productId, String facilityCode) {
        try {
            String redisKey = PickingConsumerConstant.FAST_PICKING_PID_KEY + facilityCode;
            log.info("[{}, isPidEligibleForFastPicking] The redis key formed is {} for facility {}",this.getClass().getSimpleName(), redisKey,facilityCode);
            List<String> fastPickingList = new ArrayList<>();
            if(cacheDAO.hasKey(redisKey)) {
                log.info("[{}, isPidEligibleForFastPicking] Value is being fetched from redis");
                fastPickingList = cacheDAO.get(redisKey,List.class);
            }else {
                fastPickingList = systemPreferenceService.getValuesAsList(PickingConsumerConstant.FAST_PICKING_PID_GROUP, redisKey);
                if(!CollectionUtils.isEmpty(fastPickingList)) {
                    log.info("[{}, isPidEligibleForFastPicking] The fast picking pid list formed is {}",this.getClass(),fastPickingList);
                    cacheDAO.putVal(redisKey,fastPickingList,Long.valueOf(fastPickingRedisKeyTTl),TimeUnit.HOURS);
                }
            }
            if(Objects.isNull(fastPickingList) || CollectionUtils.isEmpty(fastPickingList)) {
                log.error("[{}, isPidEligibleForFastPicking] The fast picking list formed is empty hence assuming pid is not a fast moving pid");
                return  false;
            }
            log.info("[{},isPidEligibleForFastPicking] The fast picking list formed is {} and product_id is {} and is present {}",this.getClass().getSimpleName(), fastPickingList,productId,fastPickingList.contains(Integer.toString(productId)));
            return fastPickingList.contains(Integer.toString(productId));
        }catch (Exception e) {
            log.error("[{}, isPidEligibleForFastPicking] Unable to check if Pid {} is fast picking pid for facility {} due to error {}",
                    this.getClass().getSimpleName(),productId,facilityCode,e);
            return false;
        }
    }

    private void updateLensDetails(PicklistOrderItem picklistOrderItem, OrderItemResponse orderItemResponse,
                                   String rePickStatus) {
        log.info("Inside updateLensDetails increment id {}, item type {}, rePickStatus: {}",
                picklistOrderItem.getIncrementId(), orderItemResponse.getItemType(), rePickStatus);
        try {
            if (orderItemResponse.getPower() != null &&
                    (orderItemResponse.getItemType().equals(ItemType.LEFTLENS) || orderItemResponse.getItemType().equals(ItemType.RIGHTLENS) ||orderItemResponse.getItemType().equals(ItemType.CONTACT_LENS) )) {
                log.info("Updating Lens details for increment id {}", picklistOrderItem.getIncrementId());
                OrderItemPower orderItemPower = orderItemResponse.getPower();
                if (orderItemPower != null) {
                    PrescriptionsLenDetail prescriptionsLenDetail = picklistOrderItem.getPrescriptionsLenDetail();
                    if (Objects.isNull(prescriptionsLenDetail))
                        prescriptionsLenDetail = new PrescriptionsLenDetail();
                    prescriptionsLenDetail.setCreatedAt(new Date());
                    prescriptionsLenDetail.setCreatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
                    prescriptionsLenDetail.setAp(orderItemPower.getAp());
                    prescriptionsLenDetail.setAxis(orderItemPower.getAxis());
                    prescriptionsLenDetail.setCyl(orderItemPower.getCyl());
                    prescriptionsLenDetail.setEd(orderItemPower.getEdgeDistance());
                    prescriptionsLenDetail.setHeight(orderItemPower.getLensHeight());


                    prescriptionsLenDetail.setName(orderItemPower.getPackageData());//TODO find the right name
                    prescriptionsLenDetail.setSph(orderItemPower.getSph());
                    //adding additional details
                    prescriptionsLenDetail.setBrand(orderItemPower.getBrand());
                    prescriptionsLenDetail.setCoating(orderItemPower.getCoating());
                    prescriptionsLenDetail.setLensId(orderItemPower.getLensId());
                    prescriptionsLenDetail.setLensName(orderItemPower.getLensName());
                    prescriptionsLenDetail.setLensIndex(orderItemPower.getLensIndex());

                    if (OrderItemStatus.POWER_CHANGED.name().equals(rePickStatus)) {
                        Optional<PickingDetail> pickingDetailOptional =
                                pickingDetailRepository.findByWmsOrderItemIdAndPickingSource(picklistOrderItem.getWmsOrderItemId(), picklistOrderItem.getPickingSource());
                        pickingDetailOptional.ifPresent(pickingDetail -> {
                            pickingDetail.setStatus(Constant.PICKING_STATUS.CREATED);
                            pickingDetail.setBoxCode(null);
                            pickingDetail.setItemBarcode(null);
                            pickingDetail.setSkippedCount(0);
                            pickingDetail.setProductId(picklistOrderItem.getProductId());
                            pickingDetail.setShipmentId(picklistOrderItem.getShipmentId());
                            pickingDetailRepository.save(pickingDetail);
                            log.info("pickingDetailRepository saved: {}", pickingDetail);
                        });
                    }
                    log.info("Successfully updated Lens details for increment id {}",
                            picklistOrderItem.getIncrementId());
                    picklistOrderItem.setPrescriptionsLenDetail(prescriptionsLenDetail);
                }
            }
        } catch (Exception e) {
            log.error("Error in updateLensDetails {}", e.getMessage());
        }
    }


    @Logging
    private void fetchPicklistOrderDetailsFromExternalService(Set<Integer> productIdList, List<PicklistOrderItem> picklistOrderItemsList, String rePickStatus, boolean hhdPickingAllowed) {
        Map<Long, Product> productIdAndProductMapping = new HashMap<>();
        Map<Long, String> productIdAndTypeMapping = new LinkedHashMap<>();
        List<Map<String, Object>> productTypeMapping = new LinkedList<>();

        List<Product> productList = getProductDetails(productIdList);
        try {
            productTypeMapping = systemPreferenceService.getAllLocationCategoryMapping();
        } catch (Exception e) {
            log.error("fetchPicklistOrderDetailsFromExternalService.Exception -  " + e.getMessage(), e);
        }

        for (Product product : productList) {
            if (product != null) {
                productIdAndProductMapping.put(product.getProductId(), product);
                Integer classificationId = product.getClassification();
                String type = systemPreferenceService.getProductTypeForClassificationId(classificationId, productTypeMapping);
                log.info("classificationId: " + classificationId + " | type: " + type);
                productIdAndTypeMapping.put(product.getProductId(), type);
            }
        }
        productIdAndTypeMapping.forEach((k, v) -> log.info("Product Type Key: " + k + ": Value: " + v));

        savePicklistOrderItemFromExternalService(productIdAndProductMapping, productIdAndTypeMapping,
                picklistOrderItemsList, rePickStatus, hhdPickingAllowed);
    }

    public boolean unffTLPOrder(List<PicklistOrderItem> picklistOrderItemsList) {
        return picklistOrderItemsList.stream().anyMatch(picklistOrderItem -> picklistOrderItem.getPicklistOrderItemMetaDataList()
                .stream().anyMatch(picklistOrderItemMetaData -> Constant.PICKLIST_ORDER_META.TLP_UNFF_ORDER.equalsIgnoreCase(picklistOrderItemMetaData.getPairKey())));
    }

    private void savePicklistOrderItemFromExternalService(Map<Long, Product> productIdAndProductMapping, Map<Long, String> productIdAndTypeMapping,
                                                          List<PicklistOrderItem> picklistOrderItemsList,
                                                          String rePickStatus, boolean hhdPickingAllowed) {
        List<PicklistOrderItem> esSyncPicklistOrderItemsList = new ArrayList<>();
        try {
            for (PicklistOrderItem picklistOrderItem : picklistOrderItemsList) {
                boolean catalogOpsOrSystemPreferenceTypeCallFail =
                        isCatalogOpsOrSystemPreferenceTypeCallFail(productIdAndProductMapping,
                                productIdAndTypeMapping, picklistOrderItem);
                if(!catalogOpsOrSystemPreferenceTypeCallFail) {
                    updatePidLocation(rePickStatus, hhdPickingAllowed,
                            esSyncPicklistOrderItemsList, picklistOrderItem);
                }
            }

            if (!esSyncPicklistOrderItemsList.isEmpty()) {
                log.info("Syncing Picklist To ES For wmsOrderCode - {} and shippingPackageId - {}", esSyncPicklistOrderItemsList.get(0).getWmsOrderCode(), esSyncPicklistOrderItemsList.get(0).getShipmentId());
                syncInESServiceImpl.syncPicklistOrderItemT0ES(esSyncPicklistOrderItemsList);
            }
        } catch (Exception e) {
            log.error("savePicklistOrderItemFromExternalService.Exception : Message - " + e.getMessage(), e);
        }
    }

    private boolean isCatalogOpsOrSystemPreferenceTypeCallFail(Map<Long, Product> productIdAndProductMapping,
                                                               Map<Long, String> productIdAndTypeMapping, PicklistOrderItem picklistOrderItem) {
        boolean catalogOpsOrSystemPreferenceTypeCallFail = false;
        Integer productId = picklistOrderItem.getProductId();
        String productName = null, productType = null, imageUrl = null;

        if (productIdAndProductMapping.containsKey(Long.valueOf(productId))) {
              Product productDetail = productIdAndProductMapping.get(Long.valueOf(productId));
              productName = productDetail.getValue();
              imageUrl = productDetail.getProductImage();
              if (imageUrl != null && !imageUrl.startsWith("http")) {
                  imageUrl = pickingConfig.getProductImageUrlPrefix().concat(imageUrl);
              }
              if (productIdAndTypeMapping.containsKey(Long.valueOf(productId)) &&
                      (productIdAndTypeMapping.get(Long.valueOf(productId)) != null)) {
                  productType = productIdAndTypeMapping.get(Long.valueOf(productId));
              } else {
                  String message = "SYSTEM_PREFERENCE: Product type does not exist for " + productId;
                  saveDetailInPicklistOrderItemLog(picklistOrderItem.getWmsOrderItemId(), picklistOrderItem.getId(), message);
                  catalogOpsOrSystemPreferenceTypeCallFail = true;
              }
          } else {
              String message = "CATALOG_OPS: Product Details not found for " + productId;
              saveDetailInPicklistOrderItemLog(picklistOrderItem.getWmsOrderItemId(), picklistOrderItem.getId()
                      , message);
              catalogOpsOrSystemPreferenceTypeCallFail = true;
          }
        // ToDo: Remove this code when alter query has been executed
        log.info("[{}, isCatalogOpsOrSystemPreferenceTypeCallFail] The facility code for picklist order item id {} is {}",this.getClass().getSimpleName(),picklistOrderItem.getId(),picklistOrderItem.getFacility());
        if(!StringUtils.isEmpty(productName) && productName.length()>255 && isShortProductNameAllowed
                && shortNameAllowedFacility.contains(picklistOrderItem.getFacility())) {
            log.info("[{}, isCatalogOpsOrSystemPreferenceTypeCallFail] Shortening the product Name as it is exceeding the data for picklist order item id {}",this.getClass().getSimpleName(),picklistOrderItem.getId());
            productName = productName.substring(0,255);
        }
        picklistOrderItem.setProductName(productName);
        picklistOrderItem.setProductImage(imageUrl);
        picklistOrderItem.setProductType(productType);
        return catalogOpsOrSystemPreferenceTypeCallFail;
    }

    private void updatePidLocation(String rePickStatus, boolean hhdPickingAllowed,
                                   List<PicklistOrderItem> esSyncPicklistOrderItemsList, PicklistOrderItem picklistOrderItem) throws Exception {
        String locationBarcode = null;
        String asrsLocationBarcode = null;
        if (enableGetDistinctAsrsLocationBarcode) {
            log.info("enableGetDistinctAsrsLocationBarcode enabled IMS distinctLocationType: {}",
                    picklistOrderItem.getScmOrderItemId());
            FetchStockDetailsResponse fetchStockDetailsResponse =
                    fetchDistinctStockCountDetails(picklistOrderItem);

            String locationTypePriorityListKey = updateLocationPriorityListKeyForDiscardToManual(picklistOrderItem);
            log.info("locationTypePriorityListKey - {}",locationTypePriorityListKey);
            List<String> locationTypePriorityList =
                    systemPreferenceService.getValuesAsListWithFacility(Constant.SYSTEM_PREFERENCE_GROUPS.LOCATION_TYPE_PRORITY, locationTypePriorityListKey, picklistOrderItem.getFacility());
            Map<String, PickingLocationDto> locationDtoMap =
                    getLocationTypeFromImsResponse(fetchStockDetailsResponse, picklistOrderItem.getFacility());
            if (locationTypePriorityList.contains(LocationType.ASRS.getValue())) {
                PickingLocationDto pickingLocationDto =
                        locationDtoMap.getOrDefault(LocationType.ASRS.getValue(), new PickingLocationDto());
                asrsLocationBarcode = pickingLocationDto.getBarcodeLocation();
            }
            PickingLocationDto pickingLocationDto = fetchPickingLocationType(locationTypePriorityList, locationDtoMap);

            if(pickingConfig.getImsLocationInSummaryFacilities().contains(picklistOrderItem.getFacility()) && pickingConfig.getImsLocationInSummaryItemTypes().contains(picklistOrderItem.getItemType())){
                locationBarcode = updateLocationBarcodeFetchedFromIMS(picklistOrderItem,pickingLocationDto);
            }
            else if (pickingConfig.getSuperOrderFacilities().contains(picklistOrderItem.getFacility())
                    && StringUtils.isEmpty(locationBarcode)) {
                String pidDetails = String.valueOf(picklistOrderItem.getProductId());
                locationBarcode = pidDetails.substring(pidDetails.length() - 3);
                picklistOrderItem.setLocationType(LocationType.DEFAULT.name());
                log.info("superOrderDestinationFacility barcode: {}, for shipment id:{}  ", locationBarcode,
                        picklistOrderItem.getShipmentId());
            }
            else if (pickingLocationDto != null && StringUtils.isEmpty(locationBarcode)) {
                log.info("fetched distinct barcode location from ims Location type : {}, and barcode's location : {}"
                        , pickingLocationDto.getLocationType(), pickingLocationDto.getBarcodeLocation());
                picklistOrderItem.setLocationType(pickingLocationDto.getLocationType());
                picklistOrderItem.setLocationBarcode(pickingLocationDto.getBarcodeLocation());
                locationBarcode = pickingLocationDto.getBarcodeLocation();
            }
        }
        else {
            log.info("enableGetDistinctAsrsLocationBarcode disabled fetching form IMS: {}",
                    picklistOrderItem.getScmOrderItemId());
            locationBarcode = getAsrsLocationBarcode(picklistOrderItem, false);
            if (PickingConsumerConstant.SUPER_ORDER_TYPE.equalsIgnoreCase(picklistOrderItem.getOrderType())) {
                String pidDetails = String.valueOf(picklistOrderItem.getProductId());
                locationBarcode = pidDetails.substring(pidDetails.length() - 3);
                picklistOrderItem.setLocationType(LocationType.DEFAULT.name());
                log.info("Super order Location barcode: {}, for shipment id:{}  ", locationBarcode,
                        picklistOrderItem.getShipmentId());
            } else if (pickingConfig.getSuperOrderFacilities().contains(picklistOrderItem.getFacility())
                    && StringUtils.isEmpty(locationBarcode)) {
                String pidDetails = String.valueOf(picklistOrderItem.getProductId());
                locationBarcode = pidDetails.substring(pidDetails.length() - 3);
                picklistOrderItem.setLocationType(LocationType.DEFAULT.name());
                log.info("superOrderDestinationFacility barcode: {}, for shipment id:{}  ", locationBarcode,
                        picklistOrderItem.getShipmentId());
            } else {
                asrsLocationBarcode = getAsrsLocationBarcode(picklistOrderItem, true);
            }
            log.info("Location barcode: {}, for shipment id:{}  ", locationBarcode,
                    picklistOrderItem.getShipmentId());
        }

        if (locationBarcode == null) {
            Optional<PicklistOrderItemMetaData> optionalPicklistOrderItemMetaData = getTlpUnffOrderMetaData(picklistOrderItem);
            if (optionalPicklistOrderItemMetaData.isPresent()) {
                String pidDetails = String.valueOf(picklistOrderItem.getProductId());
                locationBarcode = pidDetails.substring(pidDetails.length() - 3);
                picklistOrderItem.setLocationType(LocationType.DEFAULT.name());
                log.info("Setting TLP order for non super order facility barcode: {}, for shipment id:{}  ", locationBarcode,
                        picklistOrderItem.getShipmentId());
            } else if(!isAutoMarkNotFoundAllowed) {
                String pidDetails = String.valueOf(picklistOrderItem.getProductId());
                locationBarcode = pidDetails.substring(pidDetails.length() - 3);
                picklistOrderItem.setLocationType(LocationType.DEFAULT.name());
            }
        }

        //Handle NOT Found Condition from WMS/EMS
        if (locationBarcode == null) {
            log.info("Location Barcode is null");
            String message = "LOCATION_BARCODE: Location not found in IMS for product id " +
                    picklistOrderItem.getProductId() + " and facility " + picklistOrderItem.getFacility();
            saveDetailInPicklistOrderItemLog(picklistOrderItem.getWmsOrderItemId(),
                    picklistOrderItem.getId(), message);
            picklistOrderItem.setAsrsLocationBarcode(asrsLocationBarcode);
            picklistOrderItem.setLocationType(null==asrsLocationBarcode?LocationType.DEFAULT.name():LocationType.ASRS.name());
            try {
                markPickListOrderItemLocationNotFoundInWMS(picklistOrderItem);
            } catch (Exception e) {
                log.error("ERROR: PickListOrderItem Location not found in WMS {}, {}",
                        picklistOrderItem.getId(),
                        e.getMessage());
            }
        }
        else {
            log.info("Updating picklist order item details locationBarcode:{} | {} ", locationBarcode,
                    picklistOrderItem.getProductId());
            picklistOrderItem.setLocationBarcode(locationBarcode);
            picklistOrderItem.setAsrsLocationBarcode(asrsLocationBarcode);
            if(!enableGetDistinctAsrsLocationBarcode && StringUtils.isEmpty(picklistOrderItem.getLocationType()))
                picklistOrderItem.setLocationType(null==asrsLocationBarcode?LocationType.DEFAULT.name():LocationType.ASRS.name());
            if (pickingConfig.getMakeFmsGetBarcodeHierarchyCall() != null && pickingConfig.getMakeFmsGetBarcodeHierarchyCall()) {
                log.info("Fetching Location Barcode Hierarchy from FMS for order item id " + picklistOrderItem.getWmsOrderItemId());
                picklistOrderItem.setLocationHierarchy(getLocationHierarchy(locationBarcode));
            }

            if (getEsSyncEligibilityFlag(rePickStatus, picklistOrderItem, hhdPickingAllowed)) { //ToDo:
                // Confirm if the bulk transfer doesnt have to be synced to es
                esSyncPicklistOrderItemsList.add(picklistOrderItem);
            }

        }
    }

    private static String updateLocationPriorityListKeyForDiscardToManual(PicklistOrderItem picklistOrderItem) {
        if(OrderState.DISCARD_TO_MANUAL.name().equalsIgnoreCase(picklistOrderItem.getOrderState())) {
            return Constant.PICKING_CONSTANT.LOCATION_TYPE_PRIORITY + "_" + picklistOrderItem.getFacility() + "_MANUAL";
        }
        return Constant.PICKING_CONSTANT.LOCATION_TYPE_PRIORITY + "_" + picklistOrderItem.getFacility();
    }

    private String updateLocationBarcodeFetchedFromIMS(PicklistOrderItem picklistOrderItem, PickingLocationDto pickingLocationDto) {
        if(pickingLocationDto != null) {
            log.info("Updating NXS2 : CLS location barcode from ims Location type : {}, and barcode's location : {}"
                    , pickingLocationDto.getLocationType(), pickingLocationDto.getBarcodeLocation());
            picklistOrderItem.setLocationType(pickingLocationDto.getLocationType());
            picklistOrderItem.setLocationBarcode(pickingLocationDto.getBarcodeLocation());
            return pickingLocationDto.getBarcodeLocation();
        }
        return null;
    }

    private Optional<PicklistOrderItemMetaData> getTlpUnffOrderMetaData(PicklistOrderItem picklistOrderItem) {
        return picklistOrderItem.getPicklistOrderItemMetaDataList().stream()
                .filter(item -> Constant.PICKLIST_ORDER_META.TLP_UNFF_ORDER.equalsIgnoreCase(item.getPairKey()))
                .findFirst();
    }

    private boolean getEsSyncEligibilityFlag(String rePickStatus,PicklistOrderItem picklistOrderItem,
                                             boolean hhdPickingAllowed){
        boolean addToEsPicklist=true;
        if(!StringUtils.isEmpty(rePickStatus)) {
            //add to es only if its a frame for happy flow in case of power change
            //change the repick status so that it shows up in standard picklist
            if (DBEnums.POWER_CHANGED.equals(rePickStatus) ) {
                picklistOrderItem.setRepickStatus(null);
                picklistOrderItem.setRepickCount(0);
            }
                //normal repicking everything must sync to es
                addToEsPicklist = true;

        }
        else{
            addToEsPicklist = true;

        }
//        if(Constant.PICKING_CONSTANT.TRANSFER_SOURCE.equalsIgnoreCase(picklistOrderItem.getPickingSource())) {
//            //Added the and condition to sync a JIT item to es if re-pick count is greater than 0
//            addToEsPicklist = false;
//        }

        if(picklistOrderItem.getOrderType().equals(DISTRIBUTED_ORDERS) && JITType.NON_JIT.equals(picklistOrderItem.getJitOrder())){
            picklistOrderItem.setRepickStatus("");
            if(!hhdPickingAllowed) {
                addToEsPicklist = false;
            }
        }

        log.info("The esSync flag is "+addToEsPicklist+" for pickListOrderItem with fittingId "+picklistOrderItem.getFittingId());

        return addToEsPicklist;
    }

    private boolean isNotALensItem(PicklistOrderItem picklistOrderItem) {
        boolean flag = true;
        if (ItemType.LEFTLENS.name().equals(picklistOrderItem.getItemType())
                || ItemType.RIGHTLENS.name().equals(picklistOrderItem.getItemType()))
            flag = false;
        log.info("wmsOrderItem:" + picklistOrderItem.getWmsOrderItemId() + " isFR0Item flag:" + flag);
        return flag;
    }


    @Logging
    private String isFittingRequired(OrderItemResponse orderItemResponse) {
        boolean fitting = FittingType.REQD.name().equalsIgnoreCase(orderItemResponse.getFittingType());
        return fitting ? "Yes" : "No";
    }

    @Logging
    private boolean isJitOrder(OrderItemResponse orderItemResponse) {
        return orderItemResponse.getFulfillableType() != null &&
                FulfillableType.JIT.equals(orderItemResponse.getFulfillableType());
    }


    private List<Product> getProductDetails(Set<Integer> productIdList) {
        Set<Integer> productIdListCopy = new HashSet<>(productIdList);
        List<Product> cacheProductList = new LinkedList<>(), catalogProductList = new LinkedList<>();
        List<Product> productList = new LinkedList<>();
        List<Future<?>> getAllProductDetails = new ArrayList<>();

        try {
            for (Integer id : productIdList) {
                String key = Constant.CACHE_INFO.CATALOG_PRODUCT_RESPONSE_CACHE_KEY + id;
                if (cacheDAO.hasKey(key)) {
                    // If the product details are already present in the cache, use the same
                    log.info("Fetching product details from cache for product id key" + key);
                    String productStr = (String) cacheDAO.getVal(key);
                    cacheProductList.add(ObjectHelper.getObjectMapper().readValue(productStr, Product.class));
                    productIdListCopy.remove(id);
                }
            }

            if (productIdListCopy.isEmpty()) {
                return cacheProductList;
            }

            List<CompletableFuture<Product>> compProductList = productIdListCopy.stream()
                    .map(productId -> catalogOpsConnector.getProductDetailsFromId(productId))
                    .collect(Collectors.toList());

            CompletableFuture<Void> sizeList = CompletableFuture.allOf(
                    compProductList.toArray(new CompletableFuture[compProductList.size()])
            );

            CompletableFuture<List<Product>> list = sizeList.thenApply(v -> {
                return compProductList.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());
            });

            getAllProductDetails.add(list);
            this.checkAllTasksCompleted(getAllProductDetails);
            catalogProductList = list.get();

            if (cacheProductList.isEmpty()) {
                return catalogProductList;
            } else {
                productList = Stream.concat(cacheProductList.stream(), catalogProductList.stream())
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("ERROR: Fetch Details from Catalog Ops for product " + e.getMessage());
        }
        return productList;
    }

    @Logging
    private void checkAllTasksCompleted(List<Future<?>> tasks) throws InterruptedException, ExecutionException {
        while (!getProductDetailRejectionHandler.isQueueEmpty()) {
            getProductDetailRejectionHandler.processRejectedTasks();
        }

        for (Future<?> task : tasks) {
            if (!task.isDone()) {
                task.get();
            }
        }
    }

    @Logging
    @Override
    public String getLocationBarcode(PicklistOrderItem picklistOrderItem) {
        try {
            log.info("Get location barcode from IMS for pid:{}, shipment id:{}",
                    picklistOrderItem.getProductId(), picklistOrderItem.getShipmentId());
            FetchStockDetailsRequest fetchStockDetailsRequest =
                    pickingCommonUtils.createFetchStockDetailsRequest(picklistOrderItem,
                    false);
            FetchStockDetailsResponse imsFetchStockDetailsResponse = imsConnector.fetchStockDetails(fetchStockDetailsRequest);
            if (imsFetchStockDetailsResponse.getBarcodeItemRequestList() != null &&
                    !imsFetchStockDetailsResponse.getBarcodeItemRequestList().isEmpty()) {
                log.info("Storing barcode location " + imsFetchStockDetailsResponse.getBarcodeItemRequestList());
                Optional<BarcodeItemRequest> optionalBarcodeItemRequest =
                        imsFetchStockDetailsResponse.getBarcodeItemRequestList().stream().filter(item -> Objects.nonNull(item.getLocation())).findFirst();
                return optionalBarcodeItemRequest.isPresent() ? optionalBarcodeItemRequest.get().getLocation() : "";
            } else {
                log.error("ERROR: No barcode exist in ims for location barcode for order item " + picklistOrderItem.toString());
            }
        } catch (Exception e) {
            log.error("ERROR: Fetching details from ims for location barcode for order item " + picklistOrderItem.toString() +
                    e.getMessage(),e);
        }
        return null;
    }

    @Logging
    @Override
    public String getAsrsLocationBarcode(PicklistOrderItem picklistOrderItem, boolean isAsrsLocation) {
        try {
            log.info("Get location barcode from IMS for pid:{}, shipment id:{}",
                    picklistOrderItem.getProductId(), picklistOrderItem.getShipmentId());
            FetchStockDetailsRequest fetchStockDetailsRequest =
                    pickingCommonUtils.createFetchStockDetailsRequest(picklistOrderItem,
                            isAsrsLocation);
            FetchStockDetailsResponse imsFetchStockDetailsResponse = imsConnector.fetchStockDetails(fetchStockDetailsRequest);
            if (imsFetchStockDetailsResponse.getBarcodeItemRequestList() != null &&
                    !imsFetchStockDetailsResponse.getBarcodeItemRequestList().isEmpty()) {
                log.info("Storing barcode location " + imsFetchStockDetailsResponse.getBarcodeItemRequestList());
                Optional<BarcodeItemRequest> optionalBarcodeItemRequest =
                        imsFetchStockDetailsResponse.getBarcodeItemRequestList().stream().filter(item -> Objects.nonNull(item.getLocation())).findFirst();
                return optionalBarcodeItemRequest.isPresent() ? optionalBarcodeItemRequest.get().getLocation() : "";
            } else {
                log.error("ERROR: No barcode exist in ims for location barcode for order item " + picklistOrderItem.toString());
            }
        } catch (Exception e) {
            log.error("ERROR: Fetching details from ims for location barcode for order item " + picklistOrderItem.toString() +
                    e.getMessage(),e);
        }
        return null;
    }


    @Logging
    private String getLocationHierarchy(String locationBarcode) {
        log.info("Fetch location hierarchy for barcode: " + locationBarcode);
        try {
            if (cacheDAO.hasKey(Constant.CACHE_INFO.LOCATION_HIERARCHY_RESPONSE_CACHE_KEY + locationBarcode)) {
                // If location hierarchy details are already present in the cache, use the same
                log.info("Fetching location hierarchy from cache for barcode key" +
                        Constant.CACHE_INFO.LOCATION_HIERARCHY_RESPONSE_CACHE_KEY + locationBarcode);
                try {
                    String val = (String) cacheDAO.getVal(Constant.CACHE_INFO.LOCATION_HIERARCHY_RESPONSE_CACHE_KEY + locationBarcode);
                    return ObjectHelper.getObjectMapper().readValue(val, String.class);
                } catch (Exception e) {
                    log.error("Error in getting location hierarchy details from redis  " + e.getMessage());
                    return getLocationHierarchyFromFMS(locationBarcode);
                }
            } else {
                return getLocationHierarchyFromFMS(locationBarcode);
            }
        } catch (Exception e) {
            log.error("ERROR: Fetching location barcode hierarchy for locationBarcode " + locationBarcode);
        }
        return null;
    }

    @Logging
    private String getLocationHierarchyFromFMS(String locationBarcode) {
        log.info("Fetching location hierarchy from FMS");
        try {
            // We will fetch location hierarchy details from fms
            Map<String, Object> response = fmsConnector.getLocationHierarchy(locationBarcode);
            String locationHierarchy = new ObjectMapper().writeValueAsString(response);
            cacheDAO.putVal(Constant.CACHE_INFO.LOCATION_HIERARCHY_RESPONSE_CACHE_KEY + locationBarcode,
                    locationHierarchy, Constant.CACHE_INFO.cacheTime, TimeUnit.HOURS);
            return locationHierarchy;
        } catch (Exception e) {
            log.error("ERROR: Fetching location barcode hierarchy for locationBarcode " + locationBarcode);
        }
        return null;
    }

    @Override
    @Transactional
    public void syncPicklistOrderItemInES(String createdAtFrom, String createdAtTo, String shipmentId) {
        log.info("Sync picklist order item details in ES createdAtFrom: {}, createdAtTo: {}, shipmentId: {}", createdAtFrom, createdAtTo, shipmentId);
        try {
            List<PicklistOrderItem> picklistOrderItems = new LinkedList<>();
            if (shipmentId != null && !shipmentId.isEmpty()) {
                picklistOrderItems = picklistOrderItemRepo.findByShipmentIdAndStatus(shipmentId, Constant.PICKLIST_ORDER_STATUS.CREATED);
            } else if (createdAtFrom == null && createdAtTo == null) {
                picklistOrderItems = picklistOrderItemRepo.findByStatus(Constant.PICKLIST_ORDER_STATUS.CREATED);
            } else if (createdAtFrom != null && createdAtTo != null) {
                Date createdAtFromDate = new SimpleDateFormat("dd-MM-yyyy").parse(createdAtFrom);
                Date createdAtToDate = new SimpleDateFormat("dd-MM-yyyy").parse(createdAtTo);
                log.info("Created At from and to both are not null createdAtFromDate: {}, " +
                        "createdAtToDate: {}", createdAtFromDate, createdAtToDate);
                picklistOrderItems = picklistOrderItemRepo.
                        findByStatusAndPickingSourceAndCreatedAtBetweenOrderByCreatedAtDesc(Constant.PICKLIST_ORDER_STATUS.CREATED, Constant.PICKING_CONSTANT.WMS_SOURCE,
                                createdAtFromDate, createdAtToDate);
            } else if (createdAtTo == null) {
                Date createdAtFromDate = new SimpleDateFormat("dd-MM-yyyy").parse(createdAtFrom);
                picklistOrderItems = picklistOrderItemRepo.
                        findByStatusAndPickingSourceAndCreatedAtGreaterThanEqualOrderByCreatedAtDesc(Constant.PICKLIST_ORDER_STATUS.CREATED, Constant.PICKING_CONSTANT.WMS_SOURCE,
                                createdAtFromDate);
            } else {
                Date createdAtToDate = new SimpleDateFormat("dd-MM-yyyy").parse(createdAtTo);
                picklistOrderItems = picklistOrderItemRepo.
                        findByStatusAndPickingSourceAndCreatedAtLessThanEqualOrderByCreatedAtDesc(Constant.PICKLIST_ORDER_STATUS.CREATED, Constant.PICKING_CONSTANT.WMS_SOURCE,
                                createdAtToDate);
            }

            if (picklistOrderItems.isEmpty()) {
                log.info("All picklist order item is already synced to ES for shipmentId - {}", shipmentId);
                return;
            }

            Set<Integer> productIdList = new HashSet<>();
            for (PicklistOrderItem item : picklistOrderItems) {
                productIdList.add(item.getProductId());
                log.info("Details not synced to ES for Wms Order Item Id: {}, shipment Id: {}",
                        item.getWmsOrderItemId(), item.getShipmentId());
            }

            fetchPicklistOrderDetailsFromExternalService(productIdList, picklistOrderItems, null, false);
        } catch (Exception e) {
            log.error("PicklistOrderItemServiceImpl.syncPicklistOrderItemInES.Exception : shipmentId - " + shipmentId + ", from - " + createdAtFrom + ", to - " + createdAtTo + ", Message - " + e.getMessage(), e);
            throw new PickingException("Unable to sync picklist order item details in ES, error: " + e.getMessage(),
                    "Unable to sync picklist order item",
                    PickingExceptionStatus.INTERNAL_SERVER_ERROR);
        }
    }



    @Override
    @Transactional
    public boolean processOrderStatusUpdateResponse(OrderStatusUpdateResponse orderStatusUpdateResponse) {
        log.info("OrderStatusUpdateResponse started: {}", orderStatusUpdateResponse);
        ShipmentResponse shipmentResponse = orderStatusUpdateResponse.getShipmentResponse();
        boolean isProcessed = false;
        try {
            List<PicklistOrderItem> picklistOrderItems =
                    picklistOrderItemRepo.findByShipmentId(shipmentResponse.getShippingPackageId());
            //Filtering out items that are already in terminal status
            boolean isAnyAlreadyTerminated = picklistOrderItems.stream().anyMatch(item-> terminalStatus.contains(item.getOrderState()));
            log.info("[PickListOrderItemServiceImpl,ProcessOrderStatusUpdateResponse] The pickListOrderItems for which the order state has to be updated is {}",picklistOrderItems);
            log.info("[PickListOrderItemServiceImpl,ProcessOrderStatusUpdateResponse] Is any order in terminal state {}",isAnyAlreadyTerminated);
            if(isAnyAlreadyTerminated) {
                log.info("The pickListOrderItem contains already terminated status hence a 201 case for {}",picklistOrderItems);
                isProcessed = false;
                return isProcessed;
            }
            if (!CollectionUtils.isEmpty(picklistOrderItems) && terminalStatus.contains(shipmentResponse.getStatus())) {
                List<PickingDetail> pickingDetails =
                        pickingDetailRepository.findByShipmentId(shipmentResponse.getShippingPackageId());
                log.info("[PickListOrderItemServiceImpl,ProcessOrderStatusUpdateResponse] The picking details for which the order state has to be updated are {}",pickingDetails);
                List<Long> picklistOrderItemPickedIdList = pickingDetails.stream()
                        .filter(pd -> (pd.getStatus().equalsIgnoreCase(Constant.PICKING_STATUS.PICKED)) && pd.getOrderType().equalsIgnoreCase(Constant.PICKING_ORDER_TYPE_CONSTANT.SUPER_ORDER))
                        .map(p -> p.getPicklistOrderItemId()).collect(Collectors.toList());
                log.info("List of picked item of shipment is and size is for shipment {} {} : {}",shipmentResponse.getShippingPackageId(), picklistOrderItemPickedIdList, picklistOrderItemPickedIdList.size());
                List<PicklistOrderItem> tempPickList = picklistOrderItems.stream().filter(pl -> pl.getOrderType().equalsIgnoreCase(Constant.PICKING_ORDER_TYPE_CONSTANT.SUPER_ORDER)).collect(Collectors.toList());
                if(!tempPickList.isEmpty()) {
                    tempPickList.removeIf(p -> picklistOrderItemPickedIdList.contains(p.getId()));
                    log.info("List of non picked item after removing picked item of shipment is and size is for shipment {} {} : {}", shipmentResponse.getShippingPackageId(), tempPickList, tempPickList.size());
//                    for (PicklistOrderItem picklistOrderItem : tempPickList) {
//                      markInventoryFoundAndLogMessage(picklistOrderItem);
//                    }
                }
                picklistOrderItems.stream().forEach(picklistOrderItem -> {
                    picklistOrderItem.setOrderState(shipmentResponse.getStatus());
                    picklistOrderItem.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
                    if(OrderItemStatus.REASSIGNED.name().equalsIgnoreCase(shipmentResponse.getStatus())) {
                        picklistOrderItem.setNoItemPerOrder(0);
                        picklistOrderItem.setFullFillType(FulfillableType.FULFILLABLE);
                    }
                });
                if(!CollectionUtils.isEmpty(pickingDetails)) {
                    pickingDetails.stream().forEach(pickingDetail -> {
                        pickingDetail.setUpdatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
                        pickingDetail.setStatus(shipmentResponse.getStatus());
                        pickingDetail.setFullFillType(FulfillableType.FULFILLABLE);
                    });
                }


                //Remove form elastic search if any item shipment cancalled or reassigned

                picklistOrderItemRepo.saveAll(picklistOrderItems);
                pickingDetailRepository.saveAll(pickingDetails);
                isProcessed = true;
                List<PicklistOrderItemDocument> picklistOrderItemDocuments =
                        picklistOrderItemDocumentRepository.findByShipmentId(shipmentResponse.getShippingPackageId());
                if (!CollectionUtils.isEmpty(picklistOrderItemDocuments)) {
                    picklistOrderItemDocumentRepository.deleteAll(picklistOrderItemDocuments);
                }
                log.info("OrderStatusUpdateResponse updated successfully picklistOrderItems count:{}, pickingDetails " +
                                "count:{}, picklistOrderItemDocuments count:{}", picklistOrderItems.size(),
                        pickingDetails.size(), picklistOrderItemDocuments.size());
            }
        } catch (Exception ex) {
            log.error("Exception in processOrderStatusUpdateResponse:" + ex.getMessage(), ex);
        }
        return isProcessed;
    }

    private void markInventoryFoundAndLogMessage(PicklistOrderItem picklistOrderItem) throws Exception {
        MarkInventoryFoundRequest markItemFoundRequest = inventoryAdapterConnector.createMarkInventoryFoundRequest(picklistOrderItem.getProductId(), pickingConfig.getFacilityMapping().get(picklistOrderItem.getFacility()));
        if(!inventoryAdapterConnector.markInventoryFoundInUnicom(markItemFoundRequest)) {
            log.error("[markInventoryFound] Unable tp mark inventory found for productId {}", picklistOrderItem.getProductId());
            throw new PickingException("Unable to mark inventory found in unicom for pid "+ picklistOrderItem.getProductId());
        }
        log.info("[markInventoryFound] Successfully marked inventory found productId {}", picklistOrderItem.getProductId());
    }

    private void saveDetailInPicklistOrderItemLog(Integer wmsOrderItemId, Long picklistOrderItemId, String message) {
        log.info("Save Details in picklist order item log for wmsOrderItemId:{}, picklistOrderItemId:{}, " +
                "message:{}", wmsOrderItemId, picklistOrderItemId, message);
        PicklistOrderItemLogs picklistOrderItemLogs = new PicklistOrderItemLogs();
        try {
            picklistOrderItemLogs.setWmsOrderItemId(wmsOrderItemId);
            picklistOrderItemLogs.setPicklistOrderItemId(picklistOrderItemId);
            picklistOrderItemLogs.setMessage(message);
            picklistOrderItemLogs.setCreatedBy(Constant.PICKING_CONSTANT.DEFAULT_KAFKA_CREATED);
            picklistOrderItemLogs.setCreatedAt(new Date());
            picklistOrderItemLogsRepo.save(picklistOrderItemLogs);
        } catch (Exception e) {
            log.error("saveDetailInPicklistOrderItemLog.Exception for wmsOrderItemId: " + wmsOrderItemId +
                            ", picklistOrderItemId: " + picklistOrderItemId + ", message: " + message + ", exception: " + e.getMessage(), e);
        }
    }

    public String markPickListOrderItemLocationNotFound(List<Long> idList) {
        try {
            log.info("Mark PickListOrderItem location Not found for idList " + idList.toString());

            List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepo.findByIdIn(idList);
            for (PicklistOrderItem picklistOrderItem : picklistOrderItemList) {
                try {
                    markPickListOrderItemLocationNotFoundInWMS(picklistOrderItem);
                } catch (PickingException e) {
                    log.error("Unable to mark PickListOrderItem location not found for id :{}, error :{}, displayMessage: {}",
                            picklistOrderItem.getId(), e.getMessage(), e.getDisplayMessage());
                }
            }
            return "Item Successfully marked as not found";
        } catch (PickingException e) {
            log.error("Picking Exception in markPickListOrderItemLocationNotFound {}", e.getMessage());
            throw new PickingException("Error " + e.getMessage(), "Error " + e.getDisplayMessage(), e.getPickingExceptionStatus());
        } catch (Exception e) {
            log.error("Exception in markPickListOrderItemLocationNotFound {}", e.getMessage());
            throw new PickingException("Error " + e.getMessage(), "Error " + e.getMessage(), PickingExceptionStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public void markPickListOrderItemLocationNotFoundInWMS(PicklistOrderItem picklistOrderItem) {
        try {
            log.info("Mark PickListOrderItem not found in WMS for picklistOrderItem id : {},WMS orderId {}",
                    picklistOrderItem.getId(), picklistOrderItem.getWmsOrderId());
            List<PicklistOrderItem> picklistOrderItemList = new ArrayList<>();
            picklistOrderItemList.add(picklistOrderItem);
            Messages messages = wmsConnector.createRequestAndUpdateWMSOrderPickListOrderItemStatus(picklistOrderItemList,
                    OrderItemOperation.INVENTORY_NOT_FOUND, 0);
        } catch (PickingException e) {
            log.error("Picking Error in marking PickListOrderItem not found in EMS for picking id {}, {}", picklistOrderItem.getId(), e.getMessage());
            throw new PickingException("Picking Error in marking PickListOrderItem not found in WMS for picking id " + picklistOrderItem.getId() + " " + e.getMessage(),
                    "Error in sync to WMS " + e.getDisplayMessage(), PickingExceptionStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            log.error("Error in marking PickListOrderItem not found in EMS for picking id {}, {}", picklistOrderItem.getId(), e.getMessage());
            throw new PickingException("Error in marking PickListOrderItem not found in WMS for picking id " + picklistOrderItem.getId() + " " + e.getMessage(),
                    "Error in sync to WMS " + e.getMessage(), PickingExceptionStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Logging
    private PickingLocationDto fetchPickingLocationType(
            List<String> locationTypePriorityList, Map<String, PickingLocationDto> locationDtoMap) {
        try {
            for (String locationType : locationTypePriorityList) {
                if(locationDtoMap.containsKey(locationType)) {
                    return locationDtoMap.get(locationType);
                }
            }
        } catch (Exception e) {
            log.error("ERROR: Fetching details from ims for location barcode for order item "  +
                    e.getMessage(), e);
        }
        return null;
    }

    private FetchStockDetailsResponse fetchDistinctStockCountDetails(PicklistOrderItem picklistOrderItem) throws Exception {
        log.info("Get location barcode from IMS for pid:{}, shipment id:{}",
                picklistOrderItem.getProductId(), picklistOrderItem.getShipmentId());
        FetchStockDetailsResponse imsFetchStockDetailsResponse = null;
        if(isRedisCallAllowedForIMSLocation) {
            try {
                imsFetchStockDetailsResponse = getFetchStockDetailsResponseFromRedis(picklistOrderItem);
            }catch (Exception e) {
                log.error("[]");
                imsFetchStockDetailsResponse = getFetchStockDetailsResponseFromIms(picklistOrderItem);
            }
        }
        else {
            imsFetchStockDetailsResponse = getFetchStockDetailsResponseFromIms(picklistOrderItem);
        }
        if(Objects.nonNull(imsFetchStockDetailsResponse) && !CollectionUtils.isEmpty(imsFetchStockDetailsResponse.getBarcodeItemRequestList())){
            return imsFetchStockDetailsResponse;
        }
        log.error("ERROR: No barcode exist in ims for location barcode for order item {}",picklistOrderItem);
        return null;
    }

    private FetchStockDetailsResponse getFetchStockDetailsResponseFromIms(PicklistOrderItem picklistOrderItem) throws Exception {
        FetchStockDetailsResponse imsFetchStockDetailsResponse;
        FetchStockDetailsRequest fetchStockDetailsRequest =
                pickingCommonUtils.createFetchStockDetailsRequest(picklistOrderItem,
                        false);
        imsFetchStockDetailsResponse = imsConnector.fetchDistinctStockCountDetails(fetchStockDetailsRequest);
        return imsFetchStockDetailsResponse;
    }

    private FetchStockDetailsResponse getFetchStockDetailsResponseFromRedis(PicklistOrderItem picklistOrderItem) throws Exception {
        FetchStockDetailsResponse imsFetchStockDetailsResponse;
        String key = "NEXS_Picking_Location_"+picklistOrderItem.getFacility()+"_"+ picklistOrderItem.getProductId();
        if(cacheDAO.hasKey(key)) {
            log.info("[{}, getFetchStockDetailsResponseFromRedis] The redis DB has the key {} for id {} hence using it",this.getClass().getSimpleName(),key,picklistOrderItem.getId());
            imsFetchStockDetailsResponse = cacheDAO.get(key, FetchStockDetailsResponse.class);
        }
        else {
            FetchStockDetailsRequest fetchStockDetailsRequest =
                    pickingCommonUtils.createFetchStockDetailsRequest(picklistOrderItem,
                            false);
            imsFetchStockDetailsResponse = imsConnector.fetchDistinctStockCountDetails(fetchStockDetailsRequest);
            log.info("[{}, getFetchStockDetailsResponseFromRedis] The redis DB has doesn't have the key {} for id {} hence storing it",this.getClass().getSimpleName(),key,picklistOrderItem.getId());
            cacheDAO.putVal(key,imsFetchStockDetailsResponse,pidLocationInfoTTLInMinutes,TimeUnit.MINUTES);
        }
        return imsFetchStockDetailsResponse;
    }

    private Map<String, PickingLocationDto>  getLocationTypeFromImsResponse(FetchStockDetailsResponse imsFetchStockDetailsResponse,
                                                                            String facility) throws Exception {
        Map<String,PickingLocationDto> locationDtoMap = getListOfLocationType(imsFetchStockDetailsResponse);

        return locationDtoMap;
    }

    private Map<String,PickingLocationDto> getListOfLocationType(FetchStockDetailsResponse fetchStockDetailsResponse){
        Map<String,PickingLocationDto> locationDtoMap = new HashMap<>();
        if(Objects.nonNull(fetchStockDetailsResponse)){
            for(BarcodeItemRequest barcodeItemRequest: fetchStockDetailsResponse.getBarcodeItemRequestList()){
                if(barcodeItemRequest.getLocationType() != null) {
                    locationDtoMap.put(barcodeItemRequest.getLocationType().getValue(),new PickingLocationDto(barcodeItemRequest.getLocationType().getValue(), barcodeItemRequest.getLocation()));
                }
            }
        }
        return locationDtoMap;
    }

    @Override
    public void discardAsrsWaveForManualProcessing(String shippingPackageId, String userId) {
        try {
            log.info("[{}, discardAsrsWaveForManualProcessing] The userId {} has raised request to discard shipping package id {}", this.getClass().getSimpleName(), userId, shippingPackageId);
            //Validations
            List<PickingDetail> pickingDetailList = pickingDetailRepository.findByShipmentId(shippingPackageId);
            List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepo.findByShipmentId(shippingPackageId);
            PickingSummary pickingSummary = validateIfShipmentIsEligibleForDiscard(picklistOrderItemList, pickingDetailList, shippingPackageId);
            List<PickingDetail> updatedPickingDetails = pickingDetailList.stream().filter(item -> pickingSummary.getId().equals(item.getPickingSummaryId())).collect(Collectors.toList());
            WaveDetailEntity waveDetailEntity = wavePayloadUtil.generateWaveForDiscard(picklistOrderItemList, pickingSummary, pickingDetailList, userId);
            log.info("[{}, discardAsrsWaveForManualProcessing] The wave details formed for shippingPackageID {} is {}",this.getClass().getSimpleName(),shippingPackageId,waveDetailEntity);
            if (null!=waveDetailEntity) {
                List<WavePickingItem> wavePickingItems = new ArrayList<>();
                adverbService.generateWave(waveDetailEntity.getWave().getId(), waveDetailEntity,
                        wavePickingItems,true);
                if(WaveStatus.FAILED.equals(waveDetailEntity.getWaveStatus())) {
                    log.error("[{}, discardAsrsWaveForManualProcessing] Sync to adverb failed hence rollbacking the complete transition and deleting entries of wave details for shippingPackageId {}",this.getClass().getSimpleName(),
                            shippingPackageId);
                    throw new Exception("Sync to adverb failed hence rollbacking the complete transition and deleting entries of wave details for shippingPackageId "+shippingPackageId);
                }
                updateDiscardedStatusAndClosePickingSummary(pickingDetailList, waveDetailEntity.getWave(), userId);
                waveDetailEntity.setWaveStatus(WaveStatus.SYNCED);
                waveDetailRepository.save(waveDetailEntity);
                picklistOrderItemRepo.updatePicklistOrderItemStatusById(
                        Constant.PICKLIST_ORDER_STATUS.ADVERB_DISCARD_ES_SYNC, "ASRS DISCARD item synced to ES",
                        updatedPickingDetails.stream().map(item -> item.getPicklistOrderItemId()).collect(Collectors.toList()));
            }
        }
        catch (Exception e) {
            log.error("[{}, discardAsrsWaveForManualProcessing] Unable to generate wave to discard for shipping_package_id {} due to error {}",this.getClass().getSimpleName(),shippingPackageId,e);
        }
    }

    private PickingSummary validateIfShipmentIsEligibleForDiscard(List<PicklistOrderItem> picklistOrderItemsList, List<PickingDetail> pickingDetailsList, String shippingPackageId) {
        log.info("[{}, validateIfShipmentIsEligibleForDiscard] Validating discard details for shipping package id {}",this.getClass().getSimpleName(),shippingPackageId);
        List<Integer> asrsPickingSummaryType = Arrays.asList(2,3);
        if(CollectionUtils.isEmpty(pickingDetailsList)) {
            log.error("[{}, validateIfShipmentIsEligibleForDiscard] The shippingPackageId {} doesn't have any items in picking details hence cannot create a discard event",
                    this.getClass().getSimpleName(),
                    shippingPackageId);
            throw new PickingException("The shippingPackageId "+shippingPackageId+" doesn't have any items in picking details hence cannot create a discard event");
        }
        List<PickingSummary> pickingSummaryList = pickingSummaryReadOnlyRepository.findByIdInAndStatusInAndTypeIn(
                pickingDetailsList.stream().map(item->item.getPickingSummaryId()).collect(Collectors.toList()),
                Arrays.asList("CREATED","IN_PICKING") , asrsPickingSummaryType
        );
        log.info("The picking summary is {} for shipping_package_id {}",pickingSummaryList,shippingPackageId);
        if(CollectionUtils.isEmpty(pickingSummaryList)) {
            log.error("[{}, validateIfShipmentIsEligibleForDiscard] The shippingPackageId {} doesn't have any items in asrs picking summary hence cannot create a discard event",
                    this.getClass().getSimpleName(),shippingPackageId);
            throw new PickingException("The shippingPackageId "+shippingPackageId+" doesn't have any items in asrs picking summary hence cannot create a discard event");
        }
        List<Long> pickingSummaryIdList = pickingSummaryList.stream().map(item -> item.getId()).collect(Collectors.toList());
        log.info("The picking summary Id is {} for shipping_package_id {}",pickingSummaryIdList,shippingPackageId);
        pickingDetailsList = pickingDetailsList.stream().filter(item-> pickingSummaryIdList.contains(item.getPickingSummaryId()) && null==item.getItemBarcode()).collect(Collectors.toList());
        log.info("The picking details is {} for shipping_package_id {}",pickingDetailsList,shippingPackageId);
        if(CollectionUtils.isEmpty(pickingDetailsList)) {
            log.error("[{}, validateIfShipmentIsEligibleForDiscard] The shippingPackageId {} doesn't have any item left to be picked in asrs picking summary hence cannot create a discard event",
                    this.getClass().getSimpleName(),shippingPackageId);
            throw new PickingException("The shippingPackageId "+shippingPackageId+" doesn't have any item left to be picked in asrs picking summary hence cannot create a discard event");
        }
        picklistOrderItemsList = picklistOrderItemsList.stream().filter(item-> Constant.PICKLIST_ORDER_STATUS.ADVERB_ORDER.equals(item.getStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(picklistOrderItemsList)) {
            log.error("[{}, validateIfShipmentIsEligibleForDiscard] The shippingPackageId {} doesn't have any item synced to ASRS hence cannot create a discard event",
                    this.getClass().getSimpleName(),shippingPackageId);
            throw new PickingException("The shippingPackageId "+shippingPackageId+" doesn't have any item synced to ASRS hence cannot create a discard event");
        }
        PickingSummary pickingSummary = pickingSummaryReadOnlyRepository.findById(Collections.max(pickingSummaryIdList)).orElseThrow(()-> new PickingException("Latest Picking Summary not Found"));
        log.info("[{},validateIfShipmentIsEligibleForDiscard] The shippingPackageId {} will create a discard event based on the picking summary Id {}",this.getClass().getSimpleName(),shippingPackageId,pickingSummary.getId());
        return pickingSummary;
    }

    private void updateDiscardedStatusAndClosePickingSummary(List<PickingDetail> pickingDetailsList, PickingSummary newPickingSummary, String userId) {
        try {
            for (PickingDetail pickingDetail : pickingDetailsList) {
                pickingDetail.setPickingSummaryId(newPickingSummary.getId());
                if(org.springframework.util.StringUtils.isEmpty(pickingDetail.getItemBarcode()) && !Constant.PICKING_STATUS.PICKED.equalsIgnoreCase(pickingDetail.getStatus())) {
                    pickingDetail.setStatus(Constant.PICKING_STATUS.DISCARDED);
                    pickingDetail.setSkippedBy(userId);
                }
            }
            newPickingSummary.setStatus("CLOSED");
            pickingDetailRepository.saveAll(pickingDetailsList);
            newPickingSummary = pickingSummaryRepository.save(newPickingSummary);
        } catch (Exception e) {
            log.error("Exception while updating status in picking DB for summary id - " + newPickingSummary.getId() + " | error message:" + e.getMessage(), e);
        }
    }
}
