package com.lenskart.nexs.picking.consumer.service;

import java.util.List;
import java.util.Map;

public interface SystemPreferenceService {

    List<Map<String, Object>> getAllLocationCategoryMapping() throws Exception;

    String getProductTypeForClassificationId(Integer classificationId, List<Map<String, Object>> productTypeMapping);
    List<String> getValuesAsList(String group, String key) throws Exception;

    List<String> getValuesAsListWithFacility(String group, String key,String facility) throws Exception;



}
