package com.lenskart.nexs.picking.consumer.service.impl;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.picking.constant.Constant;
import com.lenskart.nexs.picking.consumer.service.SystemPreferenceService;
import com.lenskart.nexs.picking.entity.SystemPreference;
import com.lenskart.nexs.picking.repository.SystemPreferenceRepository;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class SystemPreferenceServiceImpl implements SystemPreferenceService {

    @CustomLogger
    private Logger log;

    @Autowired
    private SystemPreferenceRepository systemPreferenceRepository;

    @Logging
    @Override
    public List<Map<String, Object>> getAllLocationCategoryMapping() throws Exception {
        log.info("Get all location category, Starting executing..");
        List<Map<String, Object>> locationMappingList = new ArrayList<Map<String, Object>>();
        Map<String, Object> locationMap = null;
        List<String> locations = getValuesAsList(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_LOCATION,
                Constant.SYSTEM_PREFERENCE_KEYS.WAREHOUSE_LOCATION);
        if (locations != null && !locations.isEmpty()) {
            for (String location : locations) {
                locationMap = new HashMap<>();
                List<String> classifications = getValuesAsList(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_CLASSIFICATION, location);
                locationMap.put("location", location);
                locationMap.put("classifications", classifications);
                locationMappingList.add(locationMap);
            }
        }
        return locationMappingList;
    }

    @Override
    @Logging
    public String getProductTypeForClassificationId(Integer classificationId, List<Map<String, Object>> locationWarehouseMapList) {
        try {
            log.info("GetType classificationId : {}, mapping size : {}", classificationId,
                    locationWarehouseMapList.size());
            for (Map<String, Object> locationWarehouseMapListObject : locationWarehouseMapList) {
                List<Object> locationClassificationList = null;
                if (locationWarehouseMapListObject.containsKey("classifications") &&
                        locationWarehouseMapListObject.get("classifications") != null) {
                    locationClassificationList = (List<Object>) locationWarehouseMapListObject.get("classifications");
                }
                if (locationClassificationList != null && !locationClassificationList.isEmpty()) {
                    log.info("Get Type Classifications found : {}", locationClassificationList);
                    if (locationClassificationList.contains(String.valueOf(classificationId))) {
                        if (locationWarehouseMapListObject.containsKey("location")) {
                            return locationWarehouseMapListObject.get("location").toString();
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("GetType Exception for classificationId : {}", classificationId, e);
        }
        return null;
    }

    @Logging
    @Override
    public List<String> getValuesAsList(String group, String key) throws Exception {
        List<String> values = new ArrayList<String>();
        SystemPreference systemPreference = systemPreferenceRepository.findTopByGroupAndKey(group, key);
        if (systemPreference != null) {
            if (Constant.SYSTEM_PREFERENCE_TYPES.LIST.equals(systemPreference.getType())) {
                if (null != systemPreference && null != systemPreference.getValue() &&
                        !systemPreference.getValue().isEmpty()) {
                    values = Arrays.asList(systemPreference.getValue().split("\\s*,\\s*"));
                }
            } else {

                throw new Exception("Get value as list, For group -- " + group + " and key -- " + key +
                        " is not of type LIST. Its type is " + systemPreference.getType());
            }
        }
        return values;
    }

    @Logging
    @Override
    public List<String> getValuesAsListWithFacility(String group, String key,String facility) throws Exception {
        List<String> values = new ArrayList<String>();
        SystemPreference systemPreference = systemPreferenceRepository.findTopByGroupAndKeyAndFacility(group, key,facility);
        if (systemPreference != null) {
            if (Constant.SYSTEM_PREFERENCE_TYPES.LIST.equals(systemPreference.getType())) {
                if (null != systemPreference && null != systemPreference.getValue() &&
                        !systemPreference.getValue().isEmpty()) {
                    values = Arrays.asList(systemPreference.getValue().split("\\s*,\\s*"));
                }
            } else {

                throw new Exception("Get value as list, For group -- " + group + " and key -- " + key +
                        " is not of type LIST. Its type is " + systemPreference.getType());
            }
        }
        return values;
    }

}
