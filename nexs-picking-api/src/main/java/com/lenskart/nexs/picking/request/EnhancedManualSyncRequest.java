package com.lenskart.nexs.picking.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.picking.enums.OrderState;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EnhancedManualSyncRequest {
    
    @NotEmpty(message = "Shipping package ID list cannot be empty")
    @Size(max = 100, message = "Maximum 100 shipments allowed per request")
    private List<String> shippingPackageIdList;
    
    @JsonProperty("order_state")
    @Valid
    @NotNull(message = "Order state is required")
    private OrderState orderState;
    
    private String facility;
    
    private String reason;
    
    private String comments;
    
    @JsonProperty("force_sync")
    private Boolean forceSync = false;
    
    @JsonProperty("validate_user_permissions")
    private Boolean validateUserPermissions = true;
}
