package com.lenskart.nexs.picking.service;


import com.lenskart.nexs.picking.entity.SystemPreference;
import com.lenskart.nexs.picking.response.LocationCategoryMappingResponse;

import java.util.List;
import java.util.Map;

public interface SystemPreferenceServicePickingApi {
    SystemPreference findOneByGroupAndKey(String fast_picking, String user);

    SystemPreference findOneByGroupAndKeyAndFacility(String fast_picking, String user,String facility);

    List<Map<String, Object>> getAllLocationCategoryMapping();


    List<LocationCategoryMappingResponse> getAllLocationCategoryMappingWithFacility(String facility) throws Exception;

    List<String> getValuesAsList(String pickingChannel, String category);

    List<String> getValuesAsListWithFacility(String pickingChannel, String category,String facility) throws Exception;

    SystemPreference save(SystemPreference systemPreference);

    Boolean getBooleanValue(String group, String key);

    Boolean getBooleanValueWithFacility(String group, String key, String facility);
}
