package com.lenskart.nexs.picking.service.impl;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.picking.config.PickingConfig;
import com.lenskart.nexs.picking.constant.Constant;
import com.lenskart.nexs.picking.consumer.service.PicklistOrderItemService;
import com.lenskart.nexs.picking.dto.PickingPayload;
import com.lenskart.nexs.picking.entity.PickingDetail;
import com.lenskart.nexs.picking.entity.PickingSummary;
import com.lenskart.nexs.picking.entity.PicklistOrderItem;
import com.lenskart.nexs.picking.enums.JITType;
import com.lenskart.nexs.picking.esDocument.PicklistOrderItemDocument;
import com.lenskart.nexs.picking.exception.exception.PickingException;
import com.lenskart.nexs.picking.exception.exception.enums.PickingExceptionStatus;
import com.lenskart.nexs.picking.repository.writeonly.PickingDetailRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemDocumentRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemRepository;
import com.lenskart.nexs.picking.response.PickingDetailDTO;
import com.lenskart.nexs.picking.response.ScanItemBarcodeResponse;
import com.lenskart.nexs.picking.service.PickingDetailService;
import com.lenskart.nexs.picking.service.PickingService;
import com.lenskart.nexs.picking.service.PicklistOrderItemServicePickingApi;
import com.lenskart.nexs.picking.service.orderType.PickingTypeFactory;
import com.lenskart.nexs.picking.service.pickingSummaryType.PickingSummaryTypeFactory;
import com.lenskart.nexs.picking.utils.CommonUtil;
import com.lenskart.nexs.picking.utils.PickingUtils;
import com.lenskart.nexs.wms.enums.FulfillableType;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PickingDetailServiceImpl implements PickingDetailService {

    @CustomLogger
    private Logger log;

    @Autowired
    PickingDetailRepository pickingDetailRepository;

    @Autowired
    PicklistOrderItemDocumentRepository picklistOrderItemDocumentRepository;

    @Autowired
    PickingService pickingService;

    @Autowired
    PicklistOrderItemServicePickingApi picklistOrderItemService;

    @Autowired
    private PicklistOrderItemService picklistOrderItemServiceConsumer;

    @Autowired
    private PickingDetailSyncToES pickingDetailSyncToES;

    @Autowired
    private PickingUtils pickingUtils;

    @Autowired
    private PicklistOrderItemRepository picklistOrderItemRepo;

    @Autowired
    private PickingTypeFactory pickingDetailFactory;

    @Autowired
    private PickingSummaryTypeFactory pickingSummaryTypeFactory;

    @Value("#{'${sg.facilities:SGNXS1}'.split(',')}")
    private List<String> sgFacilites;

    @Value("#{'${picking.details.status.allowed:PICKLIST_CREATED,SKIPPED,DISCARD_TO_MANUAL}'.split(',')}")
    private List<String> allowedStatus;

    @Autowired
    private PickingConfig pickingConfig;

    private static final Set<String> VALID_ORDER_TYPES = new HashSet<>();

    static {
        VALID_ORDER_TYPES.add(Constant.PICKING_ORDER_TYPE_CONSTANT.SUPER_ORDER);
        VALID_ORDER_TYPES.add(Constant.PICKING_ORDER_TYPE_CONSTANT.DO_SO_ORDERS);
    }

    @Override
    public List<PickingDetail> getPickingDetailBySummaryIdAndStatus(Long pickingSummaryId, String status) {
        return pickingDetailRepository.findByPickingSummaryIdAndStatus(pickingSummaryId, status);
    }

    @Override
    public List<PickingDetail> saveAll(List<PickingDetail> pickingDetails) {
        List<PickingDetail> savedList = (List<PickingDetail>) pickingDetailRepository.saveAll(pickingDetails);
        return savedList;
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public List<PickingDetail> changeStatusBySummaryIdAndStatus(String setStatus, Long pickingSummaryId, String status) {
        List<PickingDetail> pickingDetails = pickingDetailRepository.findByPickingSummaryIdAndStatus(pickingSummaryId, status);
        for (PickingDetail pickingDetail : pickingDetails) {
            pickingDetail.setStatus(setStatus);
        }
        return (List<PickingDetail>) pickingDetailRepository.saveAll(pickingDetails);
    }

    @Override
    public List<PickingDetail> getPickingDetailBySummaryIdAndStatusAndOrderType(Long pickingSummaryId, String status, String orderType) {
        return pickingDetailRepository.findByPickingSummaryIdAndStatusAndOrderType(pickingSummaryId, status, orderType);
    }

    @Override
    public PickingDetail getPickingDetailFromWarehouseLocation(PickingSummary pickingSummary, PicklistOrderItemDocument warehouseLocation, String boxCode, Map<String, Object> result) {
        log.info("[getPickingDetailFromWarehouseLocation] increment_id:" + warehouseLocation.getIncrementId() + ", shipment_id:" + warehouseLocation.getShipmentId());
        // Check for Duplicate Entry
        List<String> statusList = new ArrayList<>();
        List<String> acceptableJITType = Arrays.asList(JITType.AUTO.name(), JITType.MANUAL.name(), JITType.NON_JIT.name());
        statusList.add(Constant.PICKING_STATUS.STOPPED);
        statusList.add(Constant.PICKING_STATUS.RESENT_FOR_PICKING);

        if (pickingDetailRepository.findByAssignedToNotAndPicklistOrderItemIdAndStatusNotIn(pickingSummary.getAssignedTo(), Long.valueOf(warehouseLocation.getPicklistOrderItemId()), statusList).size() == 0) {

            // Save New Entry
            Date date = new Date();
            PickingDetail pickingDetail = null;


            pickingDetail = new PickingDetail(pickingSummary.getAssignedTo(), pickingSummary.getId(), warehouseLocation.getIncrementId(), warehouseLocation.getProductId(),
                    warehouseLocation.getProductName(), warehouseLocation.getChannel(), warehouseLocation.getProductType(), warehouseLocation.getNoOfOrderItem(), warehouseLocation.getLocationBarcode(),
                    warehouseLocation.getShipmentId(), date, Constant.PICKING_STATUS.PICKLIST_CREATED, warehouseLocation.getPicklistOrderItemId(), warehouseLocation.getOrderType(), warehouseLocation.getPriority(),
                    warehouseLocation.getFacility(), warehouseLocation.getWmsOrderItemId(), warehouseLocation.getFitting(), warehouseLocation.getJitType(), warehouseLocation.isFastPicking(), warehouseLocation.getPickingCutOffTime(), warehouseLocation.isBulkOrder(), warehouseLocation.getFullFillType());

            pickingDetail.setUpdatedAt(new Timestamp(date.getTime()));

            if (!StringUtils.isEmpty(boxCode)) {
                pickingDetail.setBoxCode(boxCode);
            }
            log.info("[getPickingDetailFromWarehouseLocation] Data to be saved in picking detail for increment_id:" + pickingDetail.getIncrementId() + ", shipment_id:" + pickingDetail.getShipmentId());
            return pickingDetail;
        } else {
            log.info("[getPickingDetailFromWarehouseLocation] Duplicate Entry already present, deleting for increment_id:" + warehouseLocation.getIncrementId() + ", shipment_id:" + warehouseLocation.getShipmentId());
            picklistOrderItemDocumentRepository.delete(warehouseLocation);
            if (result.get("deletedDocuments") != null) {
                Object count = result.get("deletedDocuments");
                result.put("deletedDocuments", ((int) count + 1));
            } else
                result.put("deletedDocuments", 1);
            return null;
        }
    }

    @Override
    public List<PickingDetail> getItemByStatusAndChannelsAndTypeAndFacility(String status, List<String> channels, String userLocation, String facility) {
        return pickingDetailRepository.findAllByStatusAndChannelAndTypeInAndFacilityGroupByItemId(status, channels, userLocation, facility);
    }

    @Override
    public List<PickingDetailDTO> getPickingDetailBySummaryId(long pickingSummaryId, boolean uffCheck) throws Exception {
        try {
            log.info("GetPickingDetail for summaryId {} ", pickingSummaryId);
            List<PickingDetailDTO> pickingDetailDTOList = new ArrayList<>();
            List<PickingDetail> pickingDetails = new ArrayList<>();
            String whFacility = null;
            boolean isBulkOrderDoOrder = false;
            if(uffCheck) {
                pickingDetails = pickingDetailRepository.findByPickingSummaryIdAndFullFillTypeOrderByIdAsc(pickingSummaryId, FulfillableType.FULFILLABLE);
                if (pickingDetails == null) {
                    log.info("No Fulfillable pickingDetail found for pickingSummaryId: ", pickingSummaryId);
                    throw new PickingException("No Fulfillable pickingDetail found for pickingSummaryId: ", "No Fulfillable PickingDetail found", PickingExceptionStatus.NOT_FOUND);
                }
            } else {
                pickingDetails = pickingDetailRepository.findByPickingSummaryIdOrderByIdAsc(pickingSummaryId);
            }
            if (pickingDetails == null) {
                log.info("No pickingDetail found for pickingSummaryId: ", pickingSummaryId);
                throw new PickingException("No pickingDetail found for pickingSummaryId: ", "No PickingDetail found", PickingExceptionStatus.NOT_FOUND);
            }
            if (!CommonUtil.isBlank(pickingDetails)) {
                PickingDetail pickingDetailItem = pickingDetails.get(0);
                Map<Integer, List<String>> productIdToImageList = new HashMap<Integer, List<String>>();
                Map<Integer, Integer> oldProductIdToNewProductIds = null;
                PicklistOrderItem picklistOrderItem = picklistOrderItemService.getByPicklistOrderItemIdAndPickingSource(Long.valueOf(pickingDetailItem.getPicklistOrderItemId()), Constant.PICKING_CONSTANT.WMS_SOURCE);
                boolean isPLSummary = Objects.nonNull(picklistOrderItem.getPrescriptionsLenDetail());
                int i = 0;
                int storeId = 1;
                for (PickingDetail pickingDetail : pickingDetails) {
                    PickingDetailDTO pickingDetailDTO = new PickingDetailDTO();
                    BeanUtils.copyProperties(pickingDetail, pickingDetailDTO);
                    //Only for PL summary adding lens details information
                    if (isPLSummary) {
                        picklistOrderItem =
                                picklistOrderItemService.getByPicklistOrderItemIdAndPickingSource(Long.valueOf(pickingDetail.getPicklistOrderItemId()), Constant.PICKING_CONSTANT.WMS_SOURCE);
                        pickingDetailDTO.setLensDetails(pickingUtils.buildLensDetailModel(picklistOrderItem,
                                pickingDetail, Optional.ofNullable(picklistOrderItem.getPrescriptionsLenDetail()), false));
                    }
                    String locationBarcode = pickingDetailDTO.getLocationBarcode();
                    pickingDetailDTO.setAisle(StringUtils.isNotBlank(locationBarcode) && locationBarcode.length() >= 3 ? String.valueOf(locationBarcode.charAt(locationBarcode.length() - 3)) : "BLANK");
                    pickingDetailDTOList.add(pickingDetailDTO);
                    pickingDetail = setNoOfItemCount(pickingDetail);
                    whFacility = picklistOrderItem.getFacility();
                    isBulkOrderDoOrder = picklistOrderItem.isBulkOrder() || "DISTRIBUTED_ORDERS".equalsIgnoreCase(picklistOrderItem.getOrderType());

                    if (productIdToImageList != null && productIdToImageList.size() > 0 && storeId != 4) {
                        pickingDetail.setImageUrl(productIdToImageList.get(oldProductIdToNewProductIds.get(pickingDetail.getProductId())));
                    } else if (productIdToImageList != null && productIdToImageList.size() > 0 && storeId == 4) {
                        pickingDetail.setImageUrl(productIdToImageList.get(pickingDetail.getProductId()));
                    }
                    i++;
                }
                try {
                    Collections.sort(pickingDetailDTOList, Comparator.comparing(PickingDetailDTO::getLocationBarcode)
                            .thenComparing(PickingDetailDTO::getProductId));
                } catch (Exception ex) {
                    log.error("Unable to sort summary: ", pickingSummaryId, ex.getMessage());
                }
                updatePickingDetails(pickingDetailDTOList, whFacility,isBulkOrderDoOrder);
                return pickingDetailDTOList;
            } else {
                log.info("No pickingDetail found for pickingSummaryId: ", pickingSummaryId);
                throw new PickingException("No pickingDetail found for pickingSummaryId: ", "No Picking detail found", PickingExceptionStatus.NOT_FOUND);
            }
        } catch (PickingException exception) {
            log.error("Error while getting pickingDetail for summaryId: ", pickingSummaryId, "Error message: " + exception.getMessage(), exception);
            throw new PickingException(exception.getMessage(), exception.getDisplayMessage(), exception.getPickingExceptionStatus());
        }
    }

    private void updatePickingDetails(List<PickingDetailDTO> pickingDetailDTOList, String whFacility, boolean isBulkOrderDoOrder) {
        if (sgFacilites.contains(whFacility) && !CollectionUtils.isEmpty(pickingDetailDTOList)) {
            String classification = pickingDetailDTOList.get(0).getProductType();
            log.info("updatePickingDetails: whFacility - {}, sgClassifications - {}, classification - {}", whFacility, pickingConfig.getSgClassifications(), classification);
            if (pickingConfig.getSgClassifications().contains(classification)) {
                Collections.sort(pickingDetailDTOList, Comparator.comparing(PickingDetailDTO::getProductName)
                        .thenComparing(PickingDetailDTO::getProductId));
            }
            else if(isBulkOrderDoOrder) {
                Collections.sort(pickingDetailDTOList, Comparator.comparing(PickingDetailDTO::getAisle)
                        .thenComparing(PickingDetailDTO::getProductId));
            }
        }
    }

    @Override
    public TreeMap<String, Long> prepareConsolidatedPickingMap(List<PickingDetailDTO> pickingDetails) {
        if (!CollectionUtils.isEmpty(pickingDetails)) {
            String whFacility = pickingDetails.get(0).getFacility();
            String classification = pickingDetails.get(0).getProductType();
            log.info("prepareConsolidatedPickingMap: whFacility - {}, classification - {}", whFacility, classification);
            if (sgFacilites.contains(whFacility)) {
                if (pickingConfig.getSgClassifications().contains(classification))
                    return new TreeMap<>(pickingDetails.stream().collect(Collectors.groupingBy(PickingDetailDTO::getProductName, Collectors.counting())));
                else
                    return new TreeMap<>(pickingDetails.stream().collect(Collectors.groupingBy(PickingDetailDTO::getAisle, Collectors.counting())));
            }
        }
        return null;
    }

    private PickingDetail setNoOfItemCount(PickingDetail pickingDetail) {
        //pickingDetail.setProductQuantity(uniReportStockEntryRepository.countBySkuCodeAndItemStatus(pickingDetail.getItemId(), Constant.UNICOM_BARCODE_STATUS.GOOD_INVENTORY).intValue());
        return pickingDetailRepository.save(pickingDetail);
    }

    @Override
    public Map<String, Object> scanItemBarcode(String pickingDetailId, String barcode, String facility) throws Exception {
        try {
            log.info("[scanItemBarcode] pickingDetailId {}, barcode {}, facility {}", pickingDetailId, barcode, facility);
            return fetchPickingDetailAndSaveBarcode(pickingDetailId, barcode, facility, null);
        } catch (Exception e) {
            log.error("Unable to scan barcode pickingDetailId - " + pickingDetailId + ", barcode - " + barcode + ", facility - " + facility + ", error - " + e.getMessage(), e);
            throw new PickingException("Unable to scan barcode " + barcode + ", error " + e.getMessage(),
                    PickingExceptionStatus.BAD_REQUEST);
        }
    }

    public void pickingDetailValidation(PickingDetail pickingDetail, String pickingDetailId, String facility) {
        if (pickingDetail == null) {
            log.error("Picking detail is not found for {}", pickingDetailId);
            throw new PickingException("Picking detail is not found for " + pickingDetailId, "Picking detail not found", PickingExceptionStatus.NOT_FOUND);
        }

        if (!pickingDetail.getPickingSource().equalsIgnoreCase(Constant.PICKING_CONSTANT.WMS_SOURCE)) {
            log.error("Picking detail source is not WMS {}", pickingDetailId);
            throw new PickingException("Picking detail source is not WMS. " + pickingDetailId, "Picking detail source is not WMS.", PickingExceptionStatus.BAD_REQUEST);
        }

        if (pickingDetail.getStatus().equalsIgnoreCase(Constant.PICKING_STATUS.PICKED)) {
            log.error("Item is already Picked. PickingDetailId: " + pickingDetailId);
            throw new PickingException("Item is already Picked. PickingDetailId: " + pickingDetailId,
                    "Item is already Picked", PickingExceptionStatus.BAD_REQUEST);
        }

        if (pickingDetail.getStatus().equalsIgnoreCase(Constant.PICKING_STATUS.CANCELLED)) {
            log.error("Item is already Cancelled for PickingDetailId: " + pickingDetailId);
            throw new PickingException("Item is already Cancelled for PickingDetailId: " + pickingDetailId,
                    "Item is already Cancelled", PickingExceptionStatus.NOT_ACCEPTABLE);
        }

        if (pickingDetail.getStatus().equalsIgnoreCase(Constant.PICKING_STATUS.REASSIGNED)) {
            log.error("Item is already Reassigned for PickingDetailId: " + pickingDetailId);
            throw new PickingException("Item is already Reassigned for PickingDetailId: " + pickingDetailId,
                    "Item is already Reassigned", PickingExceptionStatus.NOT_ACCEPTABLE);
        }
        if (FulfillableType.NON_FULFILLABLE.equals(pickingDetail.getFullFillType()) && !pickingUtils.isTLPOrLPOrder(pickingDetail.getPicklistOrderItemId())) {
            log.error("The item with pickingDetailId {} has been marked unfullfillable due to inventory change. Please skip the item and move forward with other orders" + pickingDetailId);
            throw new PickingException("The item with pickingDetailId " + pickingDetailId + " has been marked unfullfillable due to inventory change. Please skip the item and move forward with other orders",
                    "The item with pickingDetailId " + pickingDetailId + " has been marked unfullfillable due to inventory change. Please skip the item and move forward with other orders", PickingExceptionStatus.NOT_ACCEPTABLE);
        }

        if (!pickingDetail.getFacility().equalsIgnoreCase(facility)) {
            log.error("Facility selected in header is wrong {}. Please select {}", facility, pickingDetail.getFacility());
            throw new PickingException("Facility selected in header is wrong " + facility + ". Please select " + pickingDetail.getFacility(), PickingExceptionStatus.BAD_REQUEST);
        }

        log.info("[pickingDetailValidation] Successfully validated pickingDetailId {}", pickingDetailId);
    }

    public void handleScanBarCodeException(PickingException exception, PickingDetail pickingDetail,
                                           String pickingDetailId) {
        log.error("Error while scanning pickingDetail item. ItemId " + pickingDetailId + " Error getMessage: " + exception.getMessage()
                + " | getDisplayMessage:" + exception.getDisplayMessage(), exception);
        try {
            if (exception.getDisplayMessage().equalsIgnoreCase(Constant.IMS_WMS_SYNC_ERROR.CANCELLED)) {
                markPickingDetailItemCanceled(pickingDetail, Constant.PICKING_STATUS.CANCELLED);
            } else if (exception.getDisplayMessage().equalsIgnoreCase(Constant.IMS_WMS_SYNC_ERROR.REASSIGNED)) {
                markPickingDetailItemCanceled(pickingDetail, Constant.PICKING_STATUS.REASSIGNED);
            } else if (exception.getDisplayMessage().equalsIgnoreCase(Constant.IMS_WMS_SYNC_ERROR.HOLD)) {
                markPickingDetailItemSkipped(pickingDetail, Constant.PICKING_STATUS.SKIPPED);
            }
            throw new PickingException("Error while scanning pickingDetail item. ItemId " + pickingDetailId + " Error: " + exception.getMessage(),
                    exception.getDisplayMessage(), exception.getPickingExceptionStatus());
        } catch (PickingException e) {
            throw new PickingException(e.getMessage(),
                    e.getDisplayMessage(), e.getPickingExceptionStatus());
        }
    }

    private void markPickingDetailItemSkipped(PickingDetail pickingDetail, String status) {
        log.info("[markPickingDetailItemSkipped] Mark picking detail item id {}, status {}, increment id {}, wms order item id {}"
                , pickingDetail.getId(), status, pickingDetail.getIncrementId(), pickingDetail.getWmsOrderItemId());
        try {
            pickingDetail.setItemBarcode(null);
            pickingDetail.setStatus(Constant.PICKING_STATUS.SKIPPED);
            pickingDetail.setSkippedReason(Constant.PICKING_STATUS.SKIPPED);
            pickingDetail.setSkippedBy(MDC.get("USER_ID"));
            pickingDetail.setSkippedDate(new Date());
            pickingDetailRepository.save(pickingDetail);
        } catch (Exception e) {
            log.error("[markPickingDetailItemSkipped] Unable to mark order id {}, status {}, increment id {}, wms order item id {}, error {}"
                    , pickingDetail.getId(), status, pickingDetail.getIncrementId(), pickingDetail.getWmsOrderItemId(), e.getMessage());
            throw new PickingException("Unable to mark order " + status + ", id {}" + pickingDetail.getId() + ", error" + e.getMessage(),
                    "Unable to mark order " + status + ", error " + e.getMessage(), PickingExceptionStatus.BAD_REQUEST);
        }
    }

    private void markPickingDetailItemCanceled(PickingDetail pickingDetail, String status) {
        log.info("[markPickingDetailItemCanceled] Mark picking detail item id {}, status {}, increment id {}, wms order item id {}"
                , pickingDetail.getId(), status, pickingDetail.getIncrementId(), pickingDetail.getWmsOrderItemId());
        try {
            pickingDetail.setStatus(status);
            pickingDetail.setUpdatedBy(MDC.get("USER_ID"));
            pickingDetailRepository.save(pickingDetail);
        } catch (Exception e) {
            log.error("[markPickingDetailItemCanceled] Unable to mark order id {}, status {}, increment id {}, wms order item id {}, error {}"
                    , pickingDetail.getId(), status, pickingDetail.getIncrementId(), pickingDetail.getWmsOrderItemId(), e.getMessage());
            throw new PickingException("Unable to mark order " + status + ", id {}" + pickingDetail.getId() + ", error" + e.getMessage(),
                    "Unable to mark order " + status + ", error " + e.getMessage(), PickingExceptionStatus.BAD_REQUEST);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public PickingDetail updateStatusPickingDetail(PickingDetail pickingDetail) {
        Date date = new Date();
        pickingDetail.setUpdatedAt(new Timestamp(date.getTime()));
        if (MDC.get("USER_ID") == null) {
            pickingDetail.setUpdatedBy(Constant.ADDVERB_CONSTANT.USER);
        } else
            pickingDetail.setUpdatedBy(MDC.get("USER_ID"));
        try {
            return pickingDetailRepository.save(pickingDetail);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public PickingDetail getItemById(long itemId) {
        return pickingDetailRepository.findById(itemId).orElse(null);
    }

    @Override
    public void moveSkipItemToES(PickingSummary pickingSummaryResult) {
        log.info("Move skip item to es for pickingSummary{} ", pickingSummaryResult.getId());
        List<String> statusList = Arrays.asList(Constant.PICKING_STATUS.SKIPPED, Constant.PICKING_STATUS.PICKLIST_CREATED);
        List<PickingDetail> pickingDetailList = pickingSummaryTypeFactory.getSummaryCompleteTypeService(pickingSummaryResult.getType())
                .getPickingDetailListFromSummary(pickingSummaryResult);

        try {
            if (pickingDetailList.isEmpty()) {
                log.info("Picking detail list is empty, summary {} for status {}",
                        pickingSummaryResult.getId(), statusList.toString());
            } else if (pickingDetailList.stream().anyMatch(pd -> Constant.PICKING_CONSTANT.TRANSFER_SOURCE.equals(pd.getPickingSource()))) {
                log.info("Picking details source is transfer so we will not sync it back ES summaryId {}",
                        pickingSummaryResult.getId());
                picklistOrderItemRepo.updatePicklistOrderItemStatusById(
                        Constant.PICKLIST_ORDER_STATUS.ADVERB_DISCARD_ES_SYNC, "Adhoc transfer wave close",
                        pickingDetailList.stream().map(item -> item.getPicklistOrderItemId()).collect(Collectors.toList()));
            } else {
                pickingDetailSyncToES.pickingDetailSyncToES(pickingDetailList);
                log.info("Picklist order item detail and picking details saved successfully for summary {}",
                        pickingSummaryResult.getId());
            }
        } catch (Exception e) {
            log.error("Unable to sync picking detail in ES for picking summary id: {}", pickingSummaryResult.getId());
            throw new PickingException("Unable to close picking summary as pickling details sync fail ES, error: " +
                    e.getMessage(), "Unable to close picking summary, picking detail sync fail in ES",
                    PickingExceptionStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public List<PickingDetail> getLensPickingDetails(Long fittingId) {
        List<PickingDetail> pickingDetails = pickingDetailRepository.findByFittingId(fittingId.intValue());
        pickingDetails =
                pickingDetails.stream().filter(item -> allowedStatus.contains(item.getStatus())
                        || Constant.PICKING_STATUS.PICKLIST_CREATED.equals(item.getStatus())
                        || Constant.PICKING_STATUS.SKIPPED.equals(item.getStatus())).collect(Collectors.toList());
        return pickingDetails;
    }

    @Override
    public Map<String, Object> scanItemBarcode(String pickingDetailId, String barcode, String facility, String boxBarcode) throws Exception {
        return fetchPickingDetailAndSaveBarcode(pickingDetailId, barcode, facility, boxBarcode);
    }


    /**
     * @param pickingSummaryId
     * @return
     * @throws Exception
     */
    @Override
    public List<PickingDetail> fetchPickingDetailsBySummaryId(Long pickingSummaryId)throws Exception {
        List<PickingDetail> pickingDetailList= pickingDetailRepository.findByPickingSummaryId(pickingSummaryId);
        if(CollectionUtils.isEmpty(pickingDetailList)) {
           log.info("[{}, fetchPickingDetailsBySummaryId] The picking detail list fetched is empty for picking summary Id {} hence throwing an error",this.getClass().getSimpleName(),
                   pickingSummaryId);
           throw new PickingException("The picking detail list fetched is empty for picking picking summary Id "+ pickingSummaryId, PickingExceptionStatus.BAD_REQUEST);
        }
        return pickingDetailList;
    }

    private Map<String, Object> fetchPickingDetailAndSaveBarcode(String pickingDetailId, String barcode,
                                                                 String facility, String boxBarcode) throws Exception {
        log.info("[fetchPickingDetailAndSaveBarcode] pickingDetailId {}, barcode {}, facility {}, boxBarcode {}",
                pickingDetailId, barcode, facility, boxBarcode);

        PickingDetail pickingDetail = pickingDetailRepository.findById(Long.valueOf(pickingDetailId)).orElse(null);
        log.info("[fetchPickingDetailAndSaveBarcode] pickingDetailId {}, barcode {}, facility {}, " +
                        "boxBarcode {}, pickingDetail {}", pickingDetailId, barcode, facility,
                boxBarcode, pickingDetail);
        pickingDetailValidation(pickingDetail, pickingDetailId, facility);

        log.info("[fetchPickingDetailAndSaveBarcode] Validate picking summary status pickingDetailId {}, barcode {}, facility {}, " +
                        "boxBarcode {}, pickingDetail {}", pickingDetailId, barcode, facility,
                boxBarcode, pickingDetail);
        PickingSummary pickingSummary = validatePickingSummaryStatus(pickingDetail);

        assert pickingDetail != null;

        PickingDetailDTO pickingDetailDTO = new PickingDetailDTO();
        BeanUtils.copyProperties(pickingDetail, pickingDetailDTO);
        PickingPayload pickingPayload =
                PickingPayload.builder().pickingDetailDTO(pickingDetailDTO).barcode(barcode).facility(facility)
                        .boxBarcode(boxBarcode).pid(pickingDetail.getProductId()).validateImsStatus(true)
                        .markBarcodeFound(Constant.PICKING_ORDER_TYPE_CONSTANT.SUPER_ORDER.equals(pickingDetail.getOrderType()))
                        .build();
        log.info("[fetchPickingDetailAndSaveBarcode] pickingPayload {}, status {}", pickingPayload, pickingDetail.getStatus());

        if (!Constant.PICKING_STATUS.SKIPPED.equalsIgnoreCase(pickingDetail.getStatus())) {
            log.info("[fetchPickingDetailAndSaveBarcode] Validated barcode in IMS/Unicom if needed {}, facility {}", barcode, facility);
            pickingDetailFactory.getOrderTypeService(Constant.PICKING_ORDER_TYPE_CONSTANT.STANDARD).preScanBarcode(pickingPayload);
            log.info("[savePickedItem] Successfully Validated barcode in Unicom if needed {}, facility {}", barcode, facility);
        }

        Map<String, Object> respose = scanItemBarcodeAndSave(pickingDetail, pickingSummary, barcode, facility, boxBarcode);
        return respose;
    }

    public static boolean isValidOrderType(String orderType) {
        return VALID_ORDER_TYPES.contains(orderType);
    }

    private Map<String, Object> scanItemBarcodeAndSave(PickingDetail pickingDetail, PickingSummary pickingSummary, String barcode,
                                                       String facility, String boxBarcode) {
        log.info("[scanItemBarcodeAndSave] Scan Item Barcode for barcode {}, pickingDetailId {}, facility {}", barcode, pickingDetail.getId(), facility);
        Map<String, Object> result = new HashMap<String, Object>();
        Long pickingDetailId = pickingDetail.getId();
        try {
            pickingDetail.setBoxCode(boxBarcode);
            PickingDetail pickingDetailResult = pickingService.savePickedItem(pickingDetail, facility, pickingSummary, barcode);
            log.info("[scanItemBarcodeAndSave] Scan Item Barcode for barcode {}, pickingDetailId {}, facility {}, pickingDetailResult {}",
                    barcode, pickingDetail.getId(), facility, pickingDetailResult);

            PickingSummary updatedSummary = pickingService.getPickingSummaryById(pickingDetail.getPickingSummaryId());
            log.info("[scanItemBarcodeAndSave] Scan Item Barcode for barcode {}, pickingDetailId {}, facility {}, updatedSummary {}",
                    barcode, pickingDetail.getId(), facility, updatedSummary);

            ScanItemBarcodeResponse scanItemBarcodeResponse = getScanItemBarcodeResponse(pickingDetail, updatedSummary);
            log.info("[scanItemBarcodeAndSave] Scan Item Barcode for barcode {}, pickingDetailId {}, facility {}, scanItemBarcodeResponse {}",
                    barcode, pickingDetail.getId(), facility, scanItemBarcodeResponse);

            result.put("pickingDetail", scanItemBarcodeResponse);
        } catch (PickingException exception) {
            handleScanBarCodeException(exception, pickingDetail, pickingDetailId.toString());
        }
        log.info("[scanItemBarcodeAndSave] Scan Item Barcode for barcode {}, pickingDetailId {}, " +
                "facility {}, result {}", barcode, pickingDetail.getId(), facility, result);
        return result;
    }

    private PickingSummary validatePickingSummaryStatus(PickingDetail pickingDetail) {
        log.info("[validatePickingSummaryStatus] pickingDetail {}", pickingDetail);
        PickingSummary pickingSummary = pickingService.getPickingSummaryById(pickingDetail.getPickingSummaryId());
        log.info("[validatePickingSummaryStatus] pickingSummary {}", pickingSummary);
        if (CommonUtil.isBlank(pickingSummary)) {
            log.error("PickingSummary not found for summaryId: {}", pickingDetail.getPickingSummaryId());
            throw new PickingException("PickingSummary not found for summaryId: " + pickingDetail.getPickingSummaryId(), "PickingSummary not found", PickingExceptionStatus.NOT_FOUND);
        } else {
            if (pickingSummary.getStatus().equalsIgnoreCase(Constant.PICKING_STATUS.CLOSED)) {
                log.error("PickingSummary is already close for pickingDetailId{} and pickingSummaryId{} ", pickingDetail.getId(), pickingDetail.getPickingSummaryId());
                throw new PickingException("PickingSummary is already close for pickingDetailId: " + pickingDetail.getId() + "and pickingSummaryId: " + pickingDetail.getPickingSummaryId(), "PickingSummary is already close", PickingExceptionStatus.SUMMARY_CLOSED);
            }
        }
        return pickingSummary;
    }

    @Override
    public ScanItemBarcodeResponse getScanItemBarcodeResponse(PickingDetail pickingDetail, PickingSummary updatedSummary) {
        ScanItemBarcodeResponse scanItemBarcodeResponse = new ScanItemBarcodeResponse();
        scanItemBarcodeResponse.setPId(pickingDetail.getProductId());
        scanItemBarcodeResponse.setTotalItems(updatedSummary.getNoOfItems());
        scanItemBarcodeResponse.setScannedItems(updatedSummary.getNoOfPickedItems());
        scanItemBarcodeResponse.setPendingItems(updatedSummary.getNoOfItems() - updatedSummary.getNoOfPickedItems() - updatedSummary.getNoOfSkippedItems());
        scanItemBarcodeResponse.setSkippedItems(updatedSummary.getNoOfSkippedItems());
        return scanItemBarcodeResponse;
    }

}
