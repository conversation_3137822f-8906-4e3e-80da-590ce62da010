package com.lenskart.nexs.picking.service.impl;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.picking.config.PickingConfig;
import com.lenskart.nexs.picking.constant.Constant;
import com.lenskart.nexs.picking.entity.PickingDetail;
import com.lenskart.nexs.picking.entity.PicklistOrderItem;
import com.lenskart.nexs.picking.entity.PicklistOrderItemMetaData;
import com.lenskart.nexs.picking.enums.OrderState;
import com.lenskart.nexs.picking.esDocument.PicklistOrderItemDocument;
import com.lenskart.nexs.picking.repository.writeonly.PickingDetailRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemDocumentRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemRepository;
import com.lenskart.nexs.wms.enums.FulfillableType;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class PickingDetailSyncToES {

    @CustomLogger
    private Logger log;

    @Autowired
    PicklistOrderItemRepository picklistOrderItemRepo;

    @Autowired
    private PicklistOrderItemDocumentRepository picklistOrderItemDocumentRepo;

    @Autowired
    private FetchStockDetailServiceImpl fetchStockDetailService;

    @Autowired
    private PickingConfig pickingConfig;

    @Autowired
    private PickingDetailRepository pickingDetailRepo;
    @Value("#{'${tlp.es.synced.not.allowed.facility:NXS2}'.split(',')}")
    private List<String> tlpEsSyncedNotAllowedFacility;

    @Value("#{'${lo.es.synced.not.allowed.facility:NXS2}'.split(',')}")
    private List<String> loEsSyncNotAllowedFacility;

    @Value("#{'${picking.skip.location.update.state:DISCARD_TO_MANUAL}'.split(',')}")
    private List<String> orderStateToSkipLocationUpdate;

    @Transactional(rollbackFor = Exception.class)
    public void pickingDetailSyncToES(List<PickingDetail> pickingDetailList) {
        List<Long> picklistOrderItemId = new LinkedList<>();
//      Add PICKLIST_CREATED/Pending item directly to ES
//      If Skipped Reason is "Not found" directly sync it to Supervisor panel
//      If Skipped due to other reason, check that how many times it already got synced to ES

        List<PickingDetail> updatedPickingDetailList = new LinkedList<>();
        for (PickingDetail pickingDetail : pickingDetailList) {
            String status = pickingDetail.getStatus();
            log.info("[PickingDetailSyncToEs] Updating picking detail with id {} with skippedCount {} and AsrsUnallocatedCount {}",pickingDetail.getId(),pickingDetail.getSkippedCount(),pickingDetail.getAsrsUnallocatedCount());
            if(Objects.nonNull(pickingDetail.getAsrsUnallocatedCount()) && pickingDetail.getAsrsUnallocatedCount()>=pickingConfig.getMaxAddverbSkipCount() && pickingDetail.getRepickStatus()==null) {
                log.info("[PickingDetailSyncToEs] Upating picklistOrderItem with Id {} and priority {}",pickingDetail.getPicklistOrderItemId(),Constant.PICKING_DEFAULT.MAX_UNALLOCATED_PRIORITY);
                picklistOrderItemRepo.updatePicklistOrderItemPriorityById(Constant.PICKING_DEFAULT.MAX_UNALLOCATED_PRIORITY,pickingDetail.getPicklistOrderItemId());
            }
            if (status.equalsIgnoreCase(Constant.PICKING_STATUS.PICKLIST_CREATED)) {
                picklistOrderItemId.add(pickingDetail.getPicklistOrderItemId());
            } else if (status.equalsIgnoreCase(Constant.PICKING_STATUS.SKIPPED)) {
                if (pickingConfig.getSkippedItemRetryPick() > pickingDetail.getSkippedCount()) {
                    picklistOrderItemId.add(pickingDetail.getPicklistOrderItemId());
                    pickingDetail.setSkippedCount(pickingDetail.getSkippedCount() + 1);
                } else {
                    pickingDetail.setStatus(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
                }
                updatedPickingDetailList.add(pickingDetail);
            }
        }

        log.info("Sync skipped picking details to ES for picklist order item list: {}", picklistOrderItemId);
        picklistOrderItemRepo.updatePicklistOrderItemStatusById(
                Constant.PICKLIST_ORDER_STATUS.CREATED, "Picking Detail Created, Skipped Item",
                picklistOrderItemId);

        List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepo.findByIdIn(picklistOrderItemId);
//        Fetch details from IMS for multiple PID, Create request for distinct PID
        Map<Integer, String> pidAndLocationMapping = fetchStockDetailService.createBulkFetchStockDetailsRequest(picklistOrderItemList);
        List<PicklistOrderItemDocument> picklistOrderItemDocumentList = new ArrayList<>();
        for (PicklistOrderItem picklistOrderItem : picklistOrderItemList) {
            Integer pId = picklistOrderItem.getProductId();
            if (pidAndLocationMapping.containsKey(pId) && pidAndLocationMapping.get(pId) != null 
            		&& !pidAndLocationMapping.get(pId).isEmpty() && !orderStateToSkipLocationUpdate.contains(picklistOrderItem.getOrderState())) { //not updating for DISCARD_TO_MANUAL flow
                picklistOrderItem.setLocationBarcode(pidAndLocationMapping.get(pId));
            }
            PicklistOrderItemDocument picklistOrderItemDocument = picklistOrderItemDocumentRepo.findByPicklistOrderItemId(picklistOrderItem.getId());
            String esResponse = null;
            Optional<PicklistOrderItemMetaData> tlpPicklistOrderItemMetaData =
                    picklistOrderItem.getPicklistOrderItemMetaDataList().stream()
                            .filter(item -> Constant.PICKLIST_ORDER_META.TLP_UNFF_ORDER.equalsIgnoreCase(item.getPairKey())
                                    && Constant.PICKLIST_ORDER_META.TLP.equalsIgnoreCase(item.getPairValue()))
                            .findFirst();
            Optional<PicklistOrderItemMetaData> loPicklistOrderItemMetaData =
                    picklistOrderItem.getPicklistOrderItemMetaDataList().stream()
                            .filter(item -> Constant.PICKLIST_ORDER_META.LO_ORDER.equalsIgnoreCase(item.getPairKey())
                                    && Constant.PICKLIST_ORDER_META.LO.equalsIgnoreCase(item.getPairValue()))
                            .findFirst();
            if((tlpEsSyncedNotAllowedFacility.contains(picklistOrderItem.getFacility()) && tlpPicklistOrderItemMetaData.isPresent()) || FulfillableType.NON_FULFILLABLE.equals(picklistOrderItem.getFullFillType())
                    || (loEsSyncNotAllowedFacility.contains(picklistOrderItem.getFacility()) && loPicklistOrderItemMetaData.isPresent())) {
                log.info("[savePicklistOrderItemInES] syncPicklistOrderItemT0ES skipped item: {}",picklistOrderItem);
                esResponse = "SKIPPING ES SYNC as item is TLP only or any UFF item.";
                if(Objects.nonNull(picklistOrderItemDocument)) {
                    picklistOrderItemDocumentRepo.delete(picklistOrderItemDocument);
                }
            }
            else {
                esResponse = null == picklistOrderItemDocument ? savePicklistOrderItemInES(picklistOrderItem, picklistOrderItemDocumentList) : "SUCCESS";
            }
            if (esResponse.equalsIgnoreCase("SUCCESS")) {
                picklistOrderItem.setEsSyncLog("Successfully saved in ES from picking detail");
                picklistOrderItem.setStatus(Constant.PICKLIST_ORDER_STATUS.ES_SYNC);
            } else
                picklistOrderItem.setEsSyncLog(esResponse);
        }
        if(!CollectionUtils.isEmpty(picklistOrderItemDocumentList))
            picklistOrderItemDocumentRepo.saveAll(picklistOrderItemDocumentList);
        picklistOrderItemRepo.saveAll(picklistOrderItemList);
        pickingDetailRepo.saveAll(updatedPickingDetailList);
    }

    @Logging
    private String savePicklistOrderItemInES(PicklistOrderItem picklistOrderItem, List<PicklistOrderItemDocument> picklistOrderItemDocumentList) {
        log.info("Saving skipped picking detail in ES " + picklistOrderItem.toString());
        try {
            PicklistOrderItemDocument document = new PicklistOrderItemDocument();
            document.setId(picklistOrderItem.getId());

            document.setIncrementId(picklistOrderItem.getIncrementId());
            document.setProductId(picklistOrderItem.getProductId());
            document.setPicklistOrderItemId(picklistOrderItem.getId().intValue());
            document.setWmsOrderItemId(picklistOrderItem.getWmsOrderItemId());
            document.setShipmentId(picklistOrderItem.getShipmentId());

            document.setNoOfOrderItem(picklistOrderItem.getNoItemPerOrder());
            document.setProductCount(picklistOrderItem.getNoProduct());
            document.setOrderState(picklistOrderItem.getOrderState());

            document.setChannel(picklistOrderItem.getChannel());
            document.setOrderType(picklistOrderItem.getOrderType());
            document.setPriority(picklistOrderItem.getPriority());
            document.setFacility(picklistOrderItem.getFacility());
            document.setOrderCreatedAt(picklistOrderItem.getScmOrderCreatedAt());

            document.setFitting(picklistOrderItem.getFitting());
            document.setJitOrder(picklistOrderItem.getJitOrder().getJitType()>0);
            document.setJitType(picklistOrderItem.getJitOrder().getJitType());

            document.setProductName(picklistOrderItem.getProductName());
            document.setProductImage(picklistOrderItem.getProductImage());

            document.setProductType(picklistOrderItem.getProductType());
            document.setLocationBarcode(picklistOrderItem.getLocationBarcode());
            document.setLocationHierarchy(picklistOrderItem.getLocationHierarchy());
            document.setPicklistCreatedAt(picklistOrderItem.getCreatedAt());
            document.setFastPicking(picklistOrderItem.isFastPicking());
            document.setRepickStatus(picklistOrderItem.getRepickStatus()!= null ? picklistOrderItem.getRepickStatus() : "");
            if(Objects.nonNull(picklistOrderItem.getAsrsLocationBarcode())){
                document.setLocationType(Constant.PICKING_CONSTANT.ASRS);
            }else {
                document.setLocationType(Constant.PICKING_CONSTANT.MANUAL);
            }
            document.setProcessingType(picklistOrderItem.getProcessingType());
            document.setBulkOrder(picklistOrderItem.isBulkOrder());
            document.setPickingSource(picklistOrderItem.getPickingSource());

            Optional<PicklistOrderItemMetaData> optionalPicklistOrderItemMetaData =
                    picklistOrderItem.getPicklistOrderItemMetaDataList().stream()
                            .filter(item -> Constant.PICKLIST_ORDER_META.COUNRY_CODE.equalsIgnoreCase(item.getPairKey()))
                            .findFirst();
            if(optionalPicklistOrderItemMetaData.isPresent()) {
                document.setCountryCode(optionalPicklistOrderItemMetaData.get().getPairValue());
            }
            picklistOrderItemDocumentList.add(document);
//            picklistOrderItemDocumentRepo.save(document);
            log.info("Details saved in picklist order item successfully.");
            return "SUCCESS";
        } catch (Exception e) {
            log.error("ERROR: Unable to store data in elastic search " + e.getMessage());
            return e.getMessage();
        }
    }

    public void updateEsLocationTypeUsingPicklistOrderItemId(long picklistOrderItemId, String locationType)throws  Exception {
        PicklistOrderItemDocument picklistOrderItemDocument =  picklistOrderItemDocumentRepo.findByPicklistOrderItemId(picklistOrderItemId);
        picklistOrderItemDocument.setLocationType(locationType);
        picklistOrderItemDocumentRepo.save(picklistOrderItemDocument);
    }
}
