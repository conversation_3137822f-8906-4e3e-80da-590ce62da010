package com.lenskart.nexs.picking.service.impl;

import com.lenskart.nexs.baseResponse.BaseResponseModel;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.picking.config.PickingConfig;
import com.lenskart.nexs.picking.connector.WmsConnector;
import com.lenskart.nexs.picking.constant.Constant;
import com.lenskart.nexs.picking.constants.PickingConstant;
import com.lenskart.nexs.picking.constants.PickingExceptionMessage;
import com.lenskart.nexs.picking.entity.PickingDetail;
import com.lenskart.nexs.picking.entity.PickingSummary;
import com.lenskart.nexs.picking.entity.PicklistOrderItem;
import com.lenskart.nexs.picking.enums.JITType;
import com.lenskart.nexs.picking.enums.LensType;
import com.lenskart.nexs.picking.esDocument.PicklistOrderItemDocument;
import com.lenskart.nexs.picking.exception.exception.PickingException;
import com.lenskart.nexs.picking.exception.exception.enums.PickingExceptionStatus;
import com.lenskart.nexs.picking.repository.writeonly.PickingDetailRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemDocumentRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemRepository;
import com.lenskart.nexs.picking.repository.writeonly.PrescriptionsLenDetailRepository;
import com.lenskart.nexs.picking.request.FittingCompleteRequest;
import com.lenskart.nexs.picking.request.FrameValidationRequest;
import com.lenskart.nexs.picking.request.PickingStatus;
import com.lenskart.nexs.picking.request.TrayMakingPojo;
import com.lenskart.nexs.picking.response.LensDetails;
import com.lenskart.nexs.picking.response.LensPickingDetailsPageResponse;
import com.lenskart.nexs.picking.response.Messages;
import com.lenskart.nexs.picking.response.MetaResponse;
import com.lenskart.nexs.picking.response.PickingOrderDetailsResponse;
import com.lenskart.nexs.picking.response.ResponseDTO;
import com.lenskart.nexs.picking.service.LensPickingService;
import com.lenskart.nexs.picking.service.PickingSummaryService;
import com.lenskart.nexs.picking.service.PicklistOrderItemServicePickingApi;
import com.lenskart.nexs.picking.service.orderType.PickingTypeFactory;
import com.lenskart.nexs.picking.utils.PickingUtils;
import com.lenskart.nexs.wms.constants.locations.LocationConstants;
import com.lenskart.nexs.wms.enums.ItemType;
import com.lenskart.nexs.wms.enums.LocationType;
import com.lenskart.nexs.wms.enums.OrderItemOperation;
import com.lenskart.nexs.wms.enums.ProcessingType;
import com.lenskart.nexs.wms.request.UpdateOrderItem;
import com.lenskart.nexs.wms.request.UpdateOrderStatus;
import com.lenskart.nexs.wms.response.order.OrderDetailsResponse;
import com.lenskart.nexs.wms.response.order.OrderItemResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LensPickingServiceImpl implements LensPickingService {

    @Autowired
    private PicklistOrderItemRepository picklistOrderItemRepository;

    @Autowired
    private PickingDetailRepository pickingDetailRepository;

    @Autowired
    private WmsConnector wmsConnector;

    @Autowired
    private PickingServiceImpl pickingService;

    @Autowired
    private PickingDetailServiceImpl pickingDetailService;

    @Autowired
    private PickingSummaryService pickingSummaryService;

    @Autowired
    private PrescriptionsLenDetailRepository prescriptionsLenDetailRepository;

    @Autowired
    private PicklistOrderItemServicePickingApi picklistOrderItemService;

    @Autowired
    private PickingConfig pickingConfig;

    @Autowired
    private PickingUtils pickingUtils;

    @Autowired
    private PickingTypeFactory pickingDetailFactory;

    @Autowired
    private PicklistOrderItemDocumentRepository picklistOrderItemDocumentRepository;

    @Value("#{'${allowed.picklist.order.state}'.split(',')}")
    List<String> allowedPicklistOrderState;

    @Value("#{'${lens.picking.item.type.check:FRAME,SUNGLASS,SMART_GLASSES}'.split(',')}")
    List<String> lensPickingItemTypeCheck;


    @Override
    public ResponseDTO validateFrame(FrameValidationRequest frameValidateRequest) {
        log.info("validateFrame request: {}", frameValidateRequest);
        ResponseDTO responseDTO = new ResponseDTO();
        responseDTO.setData(false);
        try {
            PickingDetail pickingDetail =
                    pickingDetailRepository.findByItemBarcodeAndFittingId(frameValidateRequest.getBarCode(),
                            Integer.valueOf(frameValidateRequest.getFittingId()));
            Optional.ofNullable(pickingDetail).orElseThrow(() -> new PickingException(PickingExceptionMessage
                    .INVALID_FRAME_BAR_CODE,
                    PickingExceptionMessage.INVALID_FRAME_BAR_CODE, PickingExceptionStatus.BAD_REQUEST));
            boolean isValid = frameValidateRequest.getFittingId().equals(pickingDetail.getFittingId().toString());
            if (isValid) {
                responseDTO.setData(Boolean.TRUE);
                responseDTO.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
                log.info("validateFrame request success | fittingId: {}", pickingDetail.getFittingId());
            } else {
                log.info("validateFrame request failed");
                throw new PickingException(PickingExceptionMessage.INVALID_FITTING_ID,
                        PickingExceptionMessage.INVALID_FITTING_ID, PickingExceptionStatus.NOT_ACCEPTABLE);
            }
        } catch (PickingException ex) {
            log.error("validateFrame invalid request: {}", ex.getMessage());
            throw new PickingException(ex.getMessage(), ex.getDisplayMessage(), ex.getPickingExceptionStatus(),
                    Boolean.FALSE);
        } catch (Exception ex) {
            log.error("validateFrame unexpected error: {}", ex);
            throw new PickingException(ex.getMessage(), PickingExceptionStatus.INTERNAL_SERVER_ERROR, ex);
        }
        return responseDTO;
    }

    @Override
    public BaseResponseModel getOrderDetailAndUpdateFitting(String itemId) {
        log.info("getOrderDetailAndCache for:{}", itemId);
        BaseResponseModel responseDTO = null;
        try {
            checkIfItemIdIsABarcode(itemId);
            responseDTO = wmsConnector.getOrderDetailsByIdAndLevel(itemId, "FITTING");
            OrderDetailsResponse orderDetailsResponse = (OrderDetailsResponse) responseDTO.getData();
            OrderItemResponse orderItemResponse =
                    orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().stream()
                            .filter(item -> Objects.nonNull(item.getFittingId()) && (lensPickingItemTypeCheck.contains(item.getItemType().name())))
                            .findFirst().orElseThrow(
                                    () -> new PickingException(PickingExceptionMessage
                                            .INVALID_FITTING_ID,
                                            PickingExceptionMessage.INVALID_FITTING_ID,
                                            PickingExceptionStatus.BAD_REQUEST));
            validateOrderItemStatus(orderItemResponse);
            //fetching wms_item_code
            Long wms_item_id = orderItemResponse.getId();
            Optional<PickingDetail> pickingDetailOp =
                    pickingDetailRepository.findByWmsOrderItemIdAndPickingSource(wms_item_id.intValue(), Constant.PICKING_CONSTANT.WMS_SOURCE);
            if (pickingDetailOp.isPresent()) {
                PickingDetail pickingDetail = pickingDetailOp.get();
                pickingDetail.setCurrentLocationCode(orderItemResponse.getLocationId());
                pickingDetailRepository.save(pickingDetail);
            }
            responseDTO.setData(new PickingOrderDetailsResponse(orderItemResponse.getFittingId().longValue(), orderItemResponse.getLocationId()));
        } catch (PickingException e) {
            log.error("lensPickingComplete invalid request: {}", e.getMessage());
            throw new PickingException(e.getMessage(), e.getPickingExceptionStatus());
        } catch (Exception ex) {
            log.error("getOrderDetailAndCache exception while cache:", ex);
            if (Objects.isNull(responseDTO))
                throw new PickingException(PickingExceptionMessage.INVALID_FITTING_ID,
                        PickingExceptionStatus.BAD_REQUEST);
        }
        log.info("getOrderDetailAndCache completed itemId: {} | responseDTO:{}", itemId, responseDTO);
        return responseDTO;
    }

    /**
     *
     * @param itemId
     * @throws Exception
     * Regex to validate series BT CT AT T followed by 5 digits of numeric value
     */
    public void checkIfItemIdIsABarcode(String itemId)throws Exception {
        Pattern pattern = Pattern.compile(pickingConfig.getTraySeriesRegex());
        if (pattern.matcher(itemId).find()) {
            log.error("[{} ] The scanned id {} matches the tray regex {} . Tray barcode scan not allowed at this place",this,getClass().getSimpleName(),itemId,pickingConfig.getTraySeriesRegex());
            throw new PickingException("The scanned id "+ itemId +" matches the tray regex. Tray barcode scan not allowed at this place",
                    "The scanned id "+ itemId +" matches the tray regex. Tray barcode scan not allowed at this place",
                    PickingExceptionStatus.BAD_REQUEST);
        }
    }

    private void validateOrderItemStatus(OrderItemResponse orderItemResponse) {
        log.info("validateOrderItemStatus: {}", orderItemResponse.getStatus());
        if (!orderItemResponse.getStatus().equals(OrderItemOperation.IN_TRAY.name())) {
            throw new PickingException(PickingExceptionMessage.ORDER_ITEM_STATUS,
                    PickingExceptionMessage.ORDER_ITEM_STATUS,
                    PickingExceptionStatus.BAD_REQUEST);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public ResponseDTO scanItemBarcode(FrameValidationRequest scanRequest, String facility) {
        ResponseDTO result = new ResponseDTO();
        log.info("lens scanItemBarcode start for requestBody: {}", scanRequest);
        List<PickingDetail> pickingDetails =
                pickingDetailService.getLensPickingDetails(Long.valueOf(scanRequest.getFittingId()));
        validateLensPickingList(pickingDetails, scanRequest.getFittingId());
        Iterator<PickingDetail> iterator = pickingDetails.iterator();
        Messages messages = null;
        PickingDetail pickingDetail = null;
        PickingDetail pickingDetailItem = null;
        try {
            while (iterator.hasNext()) {
                try {
                    pickingDetailItem = iterator.next();
                    messages =  pickingService.tryLensPicking(scanRequest, pickingDetailItem);
                    if (Objects.nonNull(messages) && messages.getDisplayMessage().contains("Success200")) {
                        pickingDetail = pickingDetailItem;
                        break;
                    }
                }catch (Exception e){
                    log.error("Unable to update barcode for pickingDetails with ID {} using barcode {} due to error {}. Hence retrying for the next items",
                            pickingDetail.getId(),scanRequest.getBarCode(),e.getMessage());
                    continue;
                }
            }
        } catch (PickingException exception) {
            log.error("[LensSavePickedItem] handleScanBarCodeException :",exception.getDisplayMessage(), exception);
            pickingDetailService.handleScanBarCodeException(exception, pickingDetailItem, scanRequest.getFittingId());
        }
        if (Objects.nonNull(messages) && messages.getDisplayMessage().contains("Success200")) {
            // pickingService.updatePickingDetail(pickingDetail);
            pickingDetail.setSecondaryStatus(Constant.PICKING_STATUS.PICKED);
            pickingDetail = pickingService.updatePickingDetail(pickingDetail, scanRequest.getBarCode());
        } else if(Objects.isNull(pickingDetail)){
            log.error("Unable to update barcode for pickingDetails with fitting ID {} using barcode {}",
                    scanRequest.getFittingId(),
                    scanRequest.getBarCode());
            throw new PickingException("Scan as valid barcode, Unable to update barcode for pickingDetails with " +
                    "barcode "+scanRequest.getBarCode(),
                    "Scan as valid barcode, Unable to update barcode for pickingDetails with " +
                            "barcode "+scanRequest.getBarCode(),
                    PickingExceptionStatus.BAD_REQUEST);
        }
        PicklistOrderItem picklistOrderItem =
                picklistOrderItemService.getByPicklistOrderItemIdAndPickingSource(pickingDetail.getPicklistOrderItemId(), Constant.PICKING_CONSTANT.WMS_SOURCE);
        result.setData((Serializable) pickingUtils.buildLensDetailModel(picklistOrderItem, pickingDetail,
                Optional.ofNullable(picklistOrderItem.getPrescriptionsLenDetail()), true)
        );
        result.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
        return result;
    }



    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public ResponseDTO scanItemBarcodeV2(FrameValidationRequest scanRequest, String facility) {
        ResponseDTO result = new ResponseDTO();
        log.info("lens scanItemBarcode start for requestBody: {}", scanRequest);
        List<PickingDetail> pickingDetails =
                pickingDetailService.getLensPickingDetails(Long.valueOf(scanRequest.getFittingId()));
        validateLensPickingList(pickingDetails, scanRequest.getFittingId());
        Iterator<PickingDetail> iterator = pickingDetails.iterator();
        Messages messages = null;
        PickingDetail pickingDetail = null;
        PickingDetail pickingDetailItem = null;
        try {
            while (iterator.hasNext()) {
                pickingDetailItem = iterator.next();
                log.info("WMS check for PickingDetail id:{} for pid:{} ", pickingDetailItem.getId(),
                        pickingDetailItem.getProductId());
                pickingDetailService.pickingDetailValidation(pickingDetailItem, scanRequest.getFittingId(), facility);
                //Sync given barcode to WMS/IMS
                PicklistOrderItem picklistOrderItem =
                        picklistOrderItemService.getByPicklistOrderItemIdAndPickingSource(pickingDetailItem.getPicklistOrderItemId(), Constant.PICKING_CONSTANT.WMS_SOURCE);
                try {
                    String locationId = picklistOrderItem.getProcessingType().equalsIgnoreCase(ProcessingType.FR1.name())? LocationConstants.LOCATION_PICKING_ZONE : picklistOrderItem.getLocationBarcode();
                    messages = pickingService.syncBarcodeInWmsAndIms(pickingDetailItem,
                            picklistOrderItem,
                            scanRequest.getBarCode(), OrderItemOperation.PICKED,null,locationId);
                    log.info("WMS response:{}", messages);
                } catch (Exception ex) {
                    log.info("[WMS_check_failed] for PickingDetail id:{} for pid:{} ", pickingDetailItem.getId(),
                            pickingDetailItem.getProductId());
                    log.error("[LensSavePickedItem] Exception: Message " + ex.getMessage(), ex);
                    if (ex.getMessage().contains("BadRequest") || ex.getMessage().contains("Ims Wms sync call failed")) {
                        messages = new Messages(ex.getMessage(), "WMS BadRequest");
                    } else if (ex instanceof PickingException){
                        PickingException pickingException = (PickingException) ex;
                        throw new PickingException(pickingException.getMessage(), pickingException.getDisplayMessage(),
                                PickingExceptionStatus.BAD_REQUEST);
                    } else {
                        throw new PickingException(ex.getMessage(), ex.getMessage(),
                                PickingExceptionStatus.BAD_REQUEST);
                    }
                }
                if (Objects.nonNull(messages) && messages.getDisplayMessage().equalsIgnoreCase("Success200")) {
                    pickingDetail = pickingDetailItem;
                    break;
                }
            }
            if (Objects.nonNull(messages) && messages.getDisplayMessage().contains("Success200")) {
               // pickingService.updatePickingDetail(pickingDetail);
                pickingDetail.setSecondaryStatus(Constant.PICKING_STATUS.PICKED);
                pickingDetail = pickingService.updatePickingDetail(pickingDetail, scanRequest.getBarCode());
            } else {
                throw new PickingException("Lens Error while updating pickingDetail : " + messages, "Error in " +
                        "updating pickingDetail", PickingExceptionStatus.BAD_REQUEST);
            }
        } catch (PickingException exception) {
            log.error("[LensSavePickedItem] handleScanBarCodeException :",exception.getDisplayMessage(), exception);
            pickingDetailService.handleScanBarCodeException(exception, pickingDetailItem, scanRequest.getFittingId());
        }
        PicklistOrderItem picklistOrderItem =
                picklistOrderItemService.getByPicklistOrderItemIdAndPickingSource(pickingDetail.getPicklistOrderItemId(), Constant.PICKING_CONSTANT.WMS_SOURCE);
        result.setData((Serializable) pickingUtils.buildLensDetailModel(picklistOrderItem, pickingDetail,
                Optional.ofNullable(picklistOrderItem.getPrescriptionsLenDetail()), true)
        );
        result.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
        return result;
    }

    private void validateLensPickingList(List<PickingDetail> pickingDetails, String fittingId) {
        if (CollectionUtils.isEmpty(pickingDetails)) {
            log.error("Lens scanItemBarcode Picking detail is not found for " + fittingId);
            throw new PickingException("Picking detail is not found for " + fittingId, "Picking detail not found",
                    PickingExceptionStatus.NOT_FOUND);
        } else if (pickingDetails.size() > 2) {
            log.error("Lens scanItemBarcode Picking detail in invalid state picklist size: " + pickingDetails.size() +
                    " | fittingId:" + fittingId);
            throw new PickingException("Lens scanItemBarcode Picking detail in invalid state picklist size:" +
                    "pickingDetails.size() fittingId:" + fittingId,
                    PickingExceptionStatus.NOT_ACCEPTABLE);
        }
    }

    @Override
    @Transactional
    public ResponseDTO complete(List<FittingCompleteRequest> fittingCompleteRequest, Long summaryId, String facility) {
        ResponseDTO result = new ResponseDTO();
        try {
            log.info("lens complete start for requestBody: {}", fittingCompleteRequest);
            Messages messages = null;
            Long pickingSummaryId = null;
            String trayId = fittingCompleteRequest.get(0).getTrayId().orElse(null);
            if(trayId == null) {
                log.error("The tryId passed cannot be null");
                throw new PickingException("The tryId passed cannot be null");
            }
            UpdateOrderStatus updateOrderStatus = new UpdateOrderStatus();
            List<UpdateOrderItem> updateOrderItemList = new ArrayList<UpdateOrderItem>();
            PicklistOrderItem picklistOrderItem = null;
            PickingSummary pickingSummary;
            PickingDetail pickingDetailFirst =
                    pickingDetailRepository.findById(fittingCompleteRequest.get(0).getPickingDetailsId()).orElseThrow(() -> new PickingException(PickingExceptionMessage
                            .INVALID_DETAILS_ID,
                            PickingExceptionMessage.INVALID_DETAILS_ID,
                            PickingExceptionStatus.BAD_REQUEST));
            List<PickingDetail> pickingDetailList =
                    pickingDetailRepository.findByFittingId(pickingDetailFirst.getFittingId());
//            PickingDetail framePickingDetail =
//                    pickingDetailList.stream().filter(pickOrderItem -> Objects.nonNull(pickOrderItem.getCurrentLocationCode())).findFirst().get();
            for (FittingCompleteRequest item : fittingCompleteRequest) {

                PickingDetail pickingDetail =
                        pickingDetailRepository.findById(item.getPickingDetailsId()).orElseThrow(() -> new PickingException(PickingExceptionMessage
                                .INVALID_DETAILS_ID,
                                PickingExceptionMessage.INVALID_DETAILS_ID,
                                PickingExceptionStatus.BAD_REQUEST));
                pickingSummaryId = pickingDetail.getPickingSummaryId();
                if (item.getStatus().equals(PickingStatus.SKIPPED)) {
                    pickingDetail.setItemBarcode(null);
                    if (pickingConfig.getLensPickRetryCount() > pickingDetail.getSkippedCount()) {
                        pickingDetail.setSkippedCount(pickingDetail.getSkippedCount() + 1);
                    }
                    if (pickingConfig.getLensPickRetryCount() <= pickingDetail.getSkippedCount()) {
                        pickingDetail.setStatus(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
                    } else {
                        pickingDetail.setStatus(Constant.PICKING_STATUS.SKIPPED);
                    }
                    pickingDetail.setSkippedBy(MDC.get("USER_ID"));
                    pickingDetail.setSkippedDate(new Date());
                    pickingDetail.setSkippedReason(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
                    PickingDetail updatedPickingDetail = pickingDetailService.updateStatusPickingDetail(pickingDetail);
                } else if (item.getStatus().equals(PickingStatus.IN_TRAY)) { //IN_TRAY status
                    UpdateOrderItem updateOrderItem = new UpdateOrderItem();
                    picklistOrderItem =
                            picklistOrderItemService.getByPicklistOrderItemIdAndPickingSource(pickingDetail.getPicklistOrderItemId(), Constant.PICKING_CONSTANT.WMS_SOURCE);
                    updateOrderItem.setBarcode(pickingDetail.getItemBarcode());
                    updateOrderItem.setStatus(OrderItemOperation.IN_TRAY);
                    updateOrderItem.setItemType(ItemType.valueOf(picklistOrderItem.getItemType()));
                    updateOrderItem.setHolded(0);
                    updateOrderItem.setProductId(picklistOrderItem.getProductId());
                    updateOrderItem.setId(Long.valueOf(picklistOrderItem.getWmsOrderItemId()));
                    updateOrderItem.setLocationId(trayId);
                    updateOrderItem.setParentLocation(OrderItemOperation.IN_TRAY.name());
                    updateOrderItem.setLocationType(LocationType.TRAY);
                    updateOrderItemList.add(updateOrderItem);
                } else {
                    log.info("Invalid picking status.");
                    throw new PickingException(PickingExceptionMessage.INVALID_PICKING_STATUS,
                            PickingExceptionMessage.INVALID_PICKING_STATUS, PickingExceptionStatus.NOT_ACCEPTABLE);
                }
            }
            if (Objects.nonNull(picklistOrderItem) && !updateOrderItemList.isEmpty()) {
                updateOrderStatus.setNexsOrderId(Long.valueOf(picklistOrderItem.getWmsOrderId()));
                updateOrderStatus.setIncrementId(picklistOrderItem.getIncrementId());
                updateOrderStatus.setShippingPackageId(picklistOrderItem.getShipmentId());
                updateOrderStatus.setUpdateOrderItems(updateOrderItemList);
                updateOrderStatus.setUpdatedBy(MDC.get("USER_ID"));
                messages = wmsConnector.updateStatusInWms(updateOrderStatus);
            }
            if (Objects.nonNull(messages)) {
                log.info("WMS response messages : " + messages);
                if (messages.getDisplayMessage().contains("Success200")) {
                    pickingSummary = pickingSummaryService.getPickingSummary(pickingSummaryId);
                    if(pickingSummary.getType()!=2 && pickingSummary.getType()!=3) { // The type 2 and 3 are for ASRS picking
                        pickingSummaryService.closeSummary(pickingSummary);
                    }

                }  else {
                    throw new PickingException("Error while completing lens picking : " + messages, "Error while " +
                            "completing lens picking "
                            , PickingExceptionStatus.BAD_REQUEST);
                }
            } else {
                log.info("WMS response messages : " + messages);
                // mark Complete for all skipped item
                pickingSummary = pickingSummaryService.getPickingSummary(pickingSummaryId);
                if(pickingSummary.getType()!=2 && pickingSummary.getType()!=3) {//The type 2 and 3 are for ASRS
                    pickingSummaryService.closeSummary(pickingSummary);
                }

            }
            result.setData((Serializable) PickingConstant.SUCCESS);
            result.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
        } catch (PickingException e) {
            log.error("lensPickingComplete invalid request: {}", e.getMessage());
            throw new PickingException(e.getMessage(), e.getPickingExceptionStatus());
        } catch (Exception ex) {
            log.error("lensPickingComplete error: {}", ex);
            throw new PickingException(ex.getMessage(), ex);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Logging
    public ResponseDTO completeV2(List<FittingCompleteRequest> fittingCompleteRequest, Long summaryId,
                                  String facility, boolean isPartialPicking) {
        ResponseDTO result = new ResponseDTO();
        try {
            log.info("lens completeV2 start for requestBody: {}", fittingCompleteRequest);
            Long pickingSummaryId = null;
            PickingSummary pickingSummary;
            PickingDetail framePickingDetail = null;
            if(fittingCompleteRequest.isEmpty()) {
                log.error("[LensPickingServiceImpl, completeV2] The fitting complete request passed is empty");
                throw new PickingException("The fitting complete request passed is empty","The fitting complete request passed is empty",PickingExceptionStatus.BAD_REQUEST);
            }

            for (FittingCompleteRequest item : fittingCompleteRequest) {
                PickingDetail pickingDetail =
                        pickingDetailRepository.findById(item.getPickingDetailsId()).orElseThrow(() -> new PickingException(PickingExceptionMessage
                                .INVALID_DETAILS_ID,
                                PickingExceptionMessage.INVALID_DETAILS_ID,
                                PickingExceptionStatus.BAD_REQUEST));
                //Trim trayId input
                item.setTrayId(Optional.of(item.getTrayId().map(x-> x.trim()).orElse("")));
                if (JITType.NON_JIT.equals(pickingDetail.getJitOrder()) && !isPartialPicking) {
                    framePickingDetail = getFramePickingDetailAndValidateTrayForNonJitLenses(framePickingDetail, item, pickingDetail);
                }

                pickingSummaryId = pickingDetail.getPickingSummaryId();

                if (item.getStatus().equals(PickingStatus.SKIPPED)) {
                    pickingDetail.setItemBarcode(null);
                    if (pickingConfig.getLensPickRetryCount() > pickingDetail.getSkippedCount() && !isPartialPicking ) {
                        pickingDetail.setSkippedCount(pickingDetail.getSkippedCount() + 1);
                    }
                    pickingDetail.setStatus(
                            pickingConfig.getLensPickRetryCount() <= pickingDetail.getSkippedCount() ?
                                    Constant.PICKING_STATUS.TEMP_NOT_FOUND:Constant.PICKING_STATUS.SKIPPED
                    );
                    pickingDetail.setSkippedBy(MDC.get("USER_ID"));
                    pickingDetail.setSkippedDate(new Date());
                    pickingDetail.setSkippedReason(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
                    PickingDetail updatedPickingDetail = pickingDetailService.updateStatusPickingDetail(pickingDetail);

                } else if (item.getStatus().equals(PickingStatus.IN_TRAY)) { //IN_TRAY status

                    PickingDetail updatedPickingDetail = performTrayMakingForLens(item, pickingDetail);

                    log.info("[LensPickingServiceImpl, completeV2] The updatedPickingDetail for the request {} is {}",item.toString(),updatedPickingDetail.toString());
                    if(updatedPickingDetail == null) { //In case the message was null updatePickingDetail would be null
                        log.error("Please scan the tray Id again as tray making failed for the pickingDetailId {}",pickingDetail.getId());
                        throw new PickingException("Please scan the tray Id again as tray making failed for the pickingDetailId " + pickingDetail.getId(),
                                "Please scan the tray Id again as tray making failed for the pickingDetailId "+pickingDetail.getId(),
                                PickingExceptionStatus.NOT_ACCEPTABLE);
                    }

                } else if(PickingStatus.PICKED.equals(item.getStatus()) && isPartialPicking) {
                    log.info("FR0 or Bulk order case : NO Action- {}",item.getPickingDetailsId());
                } else {
                    log.info("Invalid picking status.");
                    throw new PickingException(PickingExceptionMessage.INVALID_PICKING_STATUS,
                            PickingExceptionMessage.INVALID_PICKING_STATUS, PickingExceptionStatus.NOT_ACCEPTABLE);
                }
            }
            // mark Complete for all skipped/IN_TRAY item
            /*  Cross Check: Potential Issue if the picking summary Id count changes from 0 in default
             * The skip count will not be updated in pickingSummary
             * Causing a mismatch in the total = picked + skipped column of pickingSummary Table
             * */
            pickingSummary = pickingSummaryService.getPickingSummary(pickingSummaryId);
            if (pickingSummary.getType() != 2 && pickingSummary.getType() != 3) { //The type 2 and 3 are for ASRS
                pickingSummaryService.closeSummary(pickingSummary);
            }

            result.setData((Serializable) PickingConstant.SUCCESS);
            result.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
        }
        catch (PickingException e) {
            log.error("lensPickingComplete invalid request: {}", e.getMessage());
            throw new PickingException(e.getMessage(), e.getPickingExceptionStatus());
        } catch (Exception ex) {
            log.error("lensPickingComplete error: {}", ex);
            throw new PickingException(ex.getMessage(), ex);
        }
        return result;
    }

    private PickingDetail performTrayMakingForLens(FittingCompleteRequest item, PickingDetail pickingDetail)throws Exception {

        if (!item.getTrayId().isPresent()) {
            log.error("Please scan a tray id for the status update {}", item.getStatus());
            throw new PickingException("Please scan a tray id for the status update " + item.getStatus(),
                    "Please scan a tray id for the status update " + item.getStatus(), PickingExceptionStatus.NOT_ACCEPTABLE);
        }

        String trayId = item.getTrayId().get();
        TrayMakingPojo trayMakingPojo = new TrayMakingPojo(pickingDetail.getItemBarcode(), trayId,pickingDetail.getId());

        Messages messages = wmsConnector.trayMakingInWms(trayMakingPojo);

        log.info("[LensPickingServiceImpl, initiateTrayMakingForLens] WMS response messages for pickingDetailId " + pickingDetail.getId() + " is : " + messages);

        if (Objects.nonNull(messages)) {
            log.info("WMS response messages : " + messages);

            if (messages.getDisplayMessage().contains("Success200")) {
                pickingDetail.setCurrentLocationCode(item.getTrayId().get());
                return pickingDetailRepository.save(pickingDetail);
            } else {
                throw new PickingException("Error while completing lens picking : " + messages, "Error while " +
                        "completing lens picking "
                        , PickingExceptionStatus.BAD_REQUEST);
            }
        }
        return  null;
    }

    private PickingDetail getFramePickingDetailAndValidateTrayForNonJitLenses(PickingDetail framePickingDetail, FittingCompleteRequest item, PickingDetail pickingDetail)throws Exception {
        if (Objects.isNull(framePickingDetail)) {
            Optional<PickingDetail> pickingDetailOptional =
                    pickingDetailRepository.findByFittingId(pickingDetail.getFittingId()).stream()
                            .filter(pickItem -> "Yes".equalsIgnoreCase(pickItem.getFitting())).findFirst();
            framePickingDetail =
                    pickingDetailOptional.orElseThrow(() -> new PickingException(PickingExceptionMessage
                            .INVALID_DETAILS_ID,
                            PickingExceptionMessage.INVALID_DETAILS_ID,
                            PickingExceptionStatus.BAD_REQUEST));
        }
        //Validation tray with frame tray id
        validationWithFrameTrayId(item, framePickingDetail);
        return framePickingDetail;
    }

    @Logging
    private void validationWithFrameTrayId(FittingCompleteRequest item, PickingDetail framePickingDetail) {
        if (Objects.nonNull(item.getTrayId()) && item.getTrayId().isPresent()
                && !item.getTrayId().get().equals(framePickingDetail.getCurrentLocationCode())) {
            throw new PickingException(PickingExceptionMessage.INVALID_TRAY_ID_SCAN,
                    PickingExceptionMessage.INVALID_TRAY_ID_SCAN, PickingExceptionStatus.BAD_REQUEST);
        }
    }

    private LensType getLensType(String itemType) {
        if (ItemType.LEFTLENS.name().equals(itemType)) {
            return LensType.LEFT;
        } else if (ItemType.RIGHTLENS.name().equals(itemType)) {
            return LensType.RIGHT;
        }
        return null;
    }

    @Transactional
    public ResponseDTO lensPickingDetails(Long fittingId, String assignedTo, int userId, String facility) throws Exception {
        log.info("fitting id: " + fittingId);
        ResponseDTO responseDTO = new ResponseDTO();
        LensPickingDetailsPageResponse response = new LensPickingDetailsPageResponse();

        try {
            List<PicklistOrderItem> prePicklistOrderItems =
                    picklistOrderItemRepository.findByFittingIdAndStatusLessThan(fittingId.intValue(),3);
            List<PicklistOrderItem> picklistOrderItems = prePicklistOrderItems.stream()
                    .filter(item -> allowedPicklistOrderState.contains(item.getOrderState())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(picklistOrderItems)) {
                log.info("[LensPickingServiceIMpl, lensPickingDetails] Information not yet updated on picking system for the fitting id  {}",fittingId);
                throw new PickingException(PickingExceptionMessage.INFORMATION_NOT_YET_UPDATED_ON_PICKING_SYSTEM+fittingId,
                        PickingExceptionMessage.INFORMATION_NOT_YET_UPDATED_ON_PICKING_SYSTEM+fittingId, PickingExceptionStatus.BAD_REQUEST);
            }
            validatePicklistOrderItemForLensPicking(picklistOrderItems,facility);
            log.info("[[LensPickingServiceIMpl, lensPickingDetails] The picklistOrderItems fetched for fittingId {} are {}",fittingId, picklistOrderItems);
            PicklistOrderItem frameOrderItem =
                    picklistOrderItems.stream().filter(picklistOrderItem -> (picklistOrderItem.getItemType().equals(ItemType.FRAME.name())
                            || picklistOrderItem.getItemType().equals(ItemType.SUNGLASS.name()))).findFirst().orElse(null);
            picklistOrderItems =
                    picklistOrderItems.stream().filter(picklistOrderItem -> (picklistOrderItem.getItemType().equals(ItemType.LEFTLENS.name())
                            || picklistOrderItem.getItemType().equals(ItemType.RIGHTLENS.name()))).collect(Collectors.toList());
            List<LensDetails> lenses = new ArrayList<>();

//            ::TODO FUTURE SUPER_ORDER if lens are SO but frame is not Future case
            int summaryType = pickingSummaryService.getSummaryType(picklistOrderItems.get(0).getOrderType());
            log.info("[LensPickingServiceIMpl, lensPickingDetails] FittingId {}, orderType {}, summaryType {}",
                    fittingId, picklistOrderItems.get(0).getOrderType(), summaryType);

            PickingSummary pickingSummary = null;
            if (!CollectionUtils.isEmpty(picklistOrderItems)) {
                Optional<PickingDetail> pickingDetailOptional =
                        pickingDetailRepository.findByWmsOrderItemIdAndPickingSource(picklistOrderItems.get(0).getWmsOrderItemId(), picklistOrderItems.get(0).getPickingSource());
                if (pickingDetailOptional.isPresent()) {
                    PickingSummary oldPickingSummary =
                            pickingSummaryService.getPickingSummary(pickingDetailOptional.get().getPickingSummaryId());
                    if (isRePickingAllow(pickingDetailOptional.get())) {
                        pickingSummary = pickingSummaryService.savePickingSummaryNew("Lens-Picker-"+assignedTo,
                                facility, summaryType);
                        pickingSummary.setStatus(Constant.PICKING_STATUS.IN_PICKING);
                        pickingSummary = pickingSummaryService.savePickingSummary(pickingSummary);
                        if(Constant.PICKING_STATUS.IN_PICKING.equalsIgnoreCase(oldPickingSummary.getStatus())
                        || Constant.PICKING_STATUS.CREATED.equalsIgnoreCase(oldPickingSummary.getStatus())) {
                            if(oldPickingSummary.getType()!=null &&
                                    ( oldPickingSummary.getType()!=2 && oldPickingSummary.getType()!=3)) {
                                //The type 2 and 3 are for ASRS
                                oldPickingSummary.setStatus(Constant.PICKING_STATUS.CLOSED);
                            }
                            pickingSummaryService.savePickingSummary(oldPickingSummary);
                        }
                    }
                }
            } else {
                throw new PickingException(PickingExceptionMessage.LEFT_AND_RIGHT_LENS_NOT_FOUND,
                        PickingExceptionMessage.LEFT_AND_RIGHT_LENS_NOT_FOUND, PickingExceptionStatus.BAD_REQUEST);
            }
            if(Objects.isNull(pickingSummary)) {
                pickingSummary =
                        pickingSummaryService.savePickingSummaryNew("LP-"+ assignedTo +"-"+new Date().getTime(),
                                facility, summaryType);
                log.info("new pickingSummary : {}",pickingSummary);
            }
            String trayId = null;
            log.info("The frameOrderItem fetched for the fittingId {} are {}",fittingId,frameOrderItem);
            if(ObjectUtils.isNotEmpty(frameOrderItem)) {
                PickingDetail framePickingDetail =
                        pickingDetailRepository.findByPicklistOrderItemId(frameOrderItem.getId());
                trayId = framePickingDetail.getCurrentLocationCode();
            }
            log.info("The trayId for the fitting id {} is {}",fittingId,trayId);
            for (PicklistOrderItem picklistOrderItem : picklistOrderItems) {

                PickingDetail pickingDetail = createOrGetPickingDetailEntity(picklistOrderItem, trayId,
                        pickingSummary.getId(), assignedTo, userId, facility);

                if (Objects.nonNull(picklistOrderItem.getPrescriptionsLenDetail())) {
                    LensDetails lensDetails = pickingUtils.buildLensDetailModel(picklistOrderItem, pickingDetail,
                            Optional.ofNullable(picklistOrderItem.getPrescriptionsLenDetail()), true);
                    lenses.add(lensDetails);
                } else {
                    log.error("Lens details not found for the given picklistOrderItemId: {}",
                            picklistOrderItem.getId());
                    throw new PickingException(PickingExceptionMessage.LENS_DETAILS_NOT_FOUND,
                            PickingExceptionMessage.LENS_DETAILS_NOT_FOUND, PickingExceptionStatus.NOT_ACCEPTABLE);
                }
            }
            //Delete lens data from ES
            List<PicklistOrderItemDocument> picklistDocument = picklistOrderItemDocumentRepository.findByPicklistOrderItemIdIn(picklistOrderItems.stream().map(item -> item.getId()).collect(Collectors.toList()));
            picklistOrderItemDocumentRepository.deleteAll(picklistDocument);

            response.setLensDetailsList(lenses);
            responseDTO.setData(response);
            responseDTO.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
            log.info("lensPickingDetails request success | fittingId: {}", fittingId);

        } catch (PickingException e) {
            log.warn("lensPickingDetails invalid request: {}", e.getMessage());
            throw new PickingException(e.getMessage(), e.getDisplayMessage(), e.getPickingExceptionStatus());
        } catch (Exception ex) {
            log.error("lensPickingDetails error: {}", ex);
            throw ex;
        }
        return responseDTO;
    }

    private void validatePicklistOrderItemForLensPicking(List<PicklistOrderItem> picklistOrderItems, String facility)throws Exception {
        if(!picklistOrderItems.get(0).getFacility().equalsIgnoreCase(facility)) {
            log.error("[{}, validatePicklistOrderItemForLensPicking] The scanned facility code {} doesnt match to the assigned facility {} for the shipment {}",
                    this.getClass().getSimpleName(), facility, picklistOrderItems.get(0).getFacility(), picklistOrderItems.get(0).getShipmentId());
            throw  new Exception("The scanned facility code "+ facility +"doesn't match to the assigned facility "+ picklistOrderItems.get(0).getFacility() +"for the shipment "+picklistOrderItems.get(0).getShipmentId() + " . Please choose correct facility");
        }
    }

    private boolean isRePickingAllow(PickingDetail pickingDetail) {
        if (pickingConfig.getLensPickRetryCount() > pickingDetail.getSkippedCount()) {
            return true;
        }
        return false;
    }

    private PickingDetail createOrGetPickingDetailEntity(PicklistOrderItem picklistOrderItem,
                                                         String trayId, Long pickingSummaryId,
                                                         String assignedTo, int userId, String facility) {
        Optional<PickingDetail> pickingDetailByWmsOrderId =
                pickingDetailRepository.findByWmsOrderItemIdAndPickingSource(picklistOrderItem.getWmsOrderItemId(), picklistOrderItem.getPickingSource());
        PickingDetail response = null;
        if (!pickingDetailByWmsOrderId.isPresent()) {
            PickingDetail pickingDetail = new PickingDetail();
            pickingDetail.setPickingSummaryId(pickingSummaryId);
            pickingDetail.setStatus(Constant.PICKING_STATUS.PICKLIST_CREATED);
            pickingDetail.setPicklistOrderItemId(picklistOrderItem.getId());
            pickingDetail.setWmsOrderItemId(picklistOrderItem.getWmsOrderItemId());
            pickingDetail.setShipmentId(picklistOrderItem.getShipmentId());
            pickingDetail.setFacility(facility);
            pickingDetail.setFittingId(picklistOrderItem.getFittingId());
            pickingDetail.setChannel(picklistOrderItem.getChannel());
            pickingDetail.setLocationHierarchy(picklistOrderItem.getLocationHierarchy());
            pickingDetail.setLocationBarcode(picklistOrderItem.getLocationBarcode());
            pickingDetail.setVersion(picklistOrderItem.getVersion());
            if(MDC.get("USER_ID")==null) {
                pickingDetail.setCreatedBy(Constant.ADDVERB_CONSTANT.USER);
            } else {
                pickingDetail.setCreatedBy(MDC.get("USER_ID"));
            }
            pickingDetail.setCreatedAt(new Date());
            pickingDetail.setIncrementId(picklistOrderItem.getIncrementId());
            pickingDetail.setOrderType(picklistOrderItem.getOrderType());
            pickingDetail.setOrderItemCount(picklistOrderItem.getNoProduct());
            pickingDetail.setProductType(picklistOrderItem.getProductType());
            pickingDetail.setAssignedTo(assignedTo);
            pickingDetail.setIncrementId(picklistOrderItem.getIncrementId());
            pickingDetail.setProductId(picklistOrderItem.getProductId());
            pickingDetail.setProductName(picklistOrderItem.getProductName());
            pickingDetail.setPriority(picklistOrderItem.getPriority());
            pickingDetail.setProductImage(picklistOrderItem.getProductImage());
            pickingDetail.setJitOrder(picklistOrderItem.getJitOrder());
            pickingDetail.setFitting(picklistOrderItem.getFitting());
            pickingDetail.setPickingSource(picklistOrderItem.getPickingSource());
            pickingDetail.setPickingCutoff(picklistOrderItem.getPickingCutoff());
            //pickingDetail.setCurrentLocationCode(framePickingDetail.getCurrentLocationCode());
            response = pickingDetailRepository.save(pickingDetail);

            picklistOrderItem.setStatus(Constant.PICKLIST_ORDER_STATUS.PICKING_DETAIL_CREATED);
            picklistOrderItemRepository.save(picklistOrderItem);
        } else {
            PickingDetail pickingDetail = pickingDetailByWmsOrderId.get();
            pickingDetail.setFacility(picklistOrderItem.getFacility());
            pickingDetail.setShipmentId(picklistOrderItem.getShipmentId());
            if (Constant.PICKING_STATUS.REASSIGNED.equalsIgnoreCase(pickingDetail.getStatus())) {
                pickingDetail.setItemBarcode(null);
                pickingDetail.setStatus(Constant.PICKING_STATUS.PICKLIST_CREATED);
                pickingDetail.setMarkPickedIms(null);
                pickingDetail.setMarkPickedWms(null);
                pickingDetail.setCurrentLocationCode(null);
            }
            pickingDetail.setOrderType(picklistOrderItem.getOrderType());
            pickingDetail.setPickingSummaryId(pickingSummaryId);
            pickingDetail.setPickingCutoff(picklistOrderItem.getPickingCutoff());
            pickingDetail.setAssignedTo(assignedTo);
            if(MDC.get("USER_ID")==null) {
                pickingDetail.setUpdatedBy(Constant.ADDVERB_CONSTANT.USER);
            } else {
                pickingDetail.setUpdatedBy(MDC.get("USER_ID"));
            }
            pickingDetail.setUpdatedAt(new Date());
            //On case of power change compare with frameDetail location and update location to null
            if(ObjectUtils.isNotEmpty(trayId) && trayId.equals(pickingDetail.getCurrentLocationCode())) {
                pickingDetail.setCurrentLocationCode(null);
            }
            pickingDetail.setPriority(picklistOrderItem.getPriority());
            pickingDetail.setLocationBarcode(picklistOrderItem.getLocationBarcode());
            response = pickingDetailRepository.save(pickingDetail);
            picklistOrderItem.setStatus(Constant.PICKLIST_ORDER_STATUS.PICKING_DETAIL_CREATED);
            picklistOrderItemRepository.save(picklistOrderItem);
        }
        return response;
    }


//    @Override
//    public ResponseDTO jitLensScan(String lensBarcode, String type)throws Exception {
//
//        /*
//            1) Lens Barcode Scan
//            2) If the type is Lens
//                -> Check both barcodes of the lens are scanned
//                    ->If yes, return the frame and lens details
//            3) If the type is Frame
//                -> Check that both the lenses have been mapped to the tray
//                    -> If yes, create a new Picking Summary for the frame
//                    -> Return the frame details along with the tray to be scanned
//        */
//        log.info("In JIT lens scan to prepare the lens and frame details");
//        List<ProcessingType> notFR0OrPLOrders = Arrays.asList(ProcessingType.FR2,ProcessingType.FR1);
//        ResponseDTO responseDTO = new ResponseDTO();
//        JITPickingResponse response = new JITPickingResponse();
//
//        // Fetch all the fitting level details for the barcode
//        OrderDetailsResponse responseModel = (OrderDetailsResponse) wmsConnector.getOrderDetailsByIdAndLevel(lensBarcode, "FITTING").getData();
//        log.info("The response model for the barcode {} is {}",lensBarcode, responseModel);
//        if(responseModel==null || responseModel.getOrderItemHeaderResponse() == null || CollectionUtils.isEmpty(responseModel.getOrderItemHeaderResponse().getOrderItemResponses())) {
//            log.error("Could not find the fitting level details for the barcode "+ lensBarcode);
//            throw new PickingException("Could not find the fitting level details for the barcode "+ lensBarcode,
//                    "Could not find the fitting level details for the barcode "+ lensBarcode,
//                    PickingExceptionStatus.BAD_REQUEST);
//        }
//
//        //Get the order item details for the fitting level of the barcode
//        List<OrderItemResponse> orderItemResponseList = responseModel.getOrderItemHeaderResponse().getOrderItemResponses();
//
//        log.info("The order item response list for the barcode {} is {}",lensBarcode, orderItemResponseList);
//        List<LensDetails> lensDetailsList = new ArrayList<>();
//        JITFrameResponse frameDetails = new JITFrameResponse();
//
//        //Iterate through each item to find the lens or frame details
//        for(OrderItemResponse orderItemResponse: orderItemResponseList) {
//          ItemType itemType = orderItemResponse.getItemType();
//          log.info("The item Type is {}",itemType);
//          if(ItemType.RIGHTLENS.equals(itemType) || ItemType.LEFTLENS.equals(itemType)) {
//              log.info("Filling lens details for the pid {}",orderItemResponse.getProduct_id());
//              LensDetails lensDetails = fillLensDetails(orderItemResponse,responseModel.getOrderId() );
//              lensDetailsList.add(lensDetails);
//          }
//
//          else if((ItemType.FRAME.equals(itemType) || ItemType.SUNGLASS.equals(itemType)) &&
//            notFR0OrPLOrders.contains(orderItemResponse.getProcessingType())){
//              log.info("Filling frame details for the pid {}", orderItemResponse.getProduct_id());
//              frameDetails  = fillFrameDetails(orderItemResponse);
//          }
//
//        }
//
//        if(type.equalsIgnoreCase("Lens")){
//            if(lensDetailsList.stream().anyMatch(item -> item.getItemBarcode()==null)) {
//                log.error("One of the lenses has not yet been assigned with the barcode and been picked");
//                throw new PickingException("One of the lenses has not yet been assigned with the barcode and been picked",
//                        "One of the lenses has not yet been assigned with the barcode and been picked",
//                        PickingExceptionStatus.BAD_REQUEST);
//            }
//            if(!lensDetailsList.stream().map(item->item.getItemBarcode()).collect(Collectors.toList()).contains(lensBarcode)) { //Just in case they scanned the frame barocde first
//                log.error("The lens barcode was not scanned");
//                throw  new PickingException("The lens barcode was not scanned", "The lens barcode was not scanned", PickingExceptionStatus.BAD_REQUEST);
//            }
//        }
//        if(type.equalsIgnoreCase("Frame")) {//Check the condition that the lenses are picked when the call is from frame picking
//            if(lensDetailsList.stream().anyMatch(item -> item.getTrayId()==null)){
//                log.error("Please do the tray making for lenses as they are not assigned to a tray");
//                throw new PickingException("Please do the tray making for lenses as they are not assigned to a tray",
//                        "Please do the tray making for lenses as they are not assigned to a tray",PickingExceptionStatus.BAD_REQUEST);
//            }
//            if(frameDetails == null) { //Check that the item was frame picked
//                log.error("Cannot get frame details");
//                throw new PickingException("Cannot get frame details",
//                        "Cannot get frame details",PickingExceptionStatus.BAD_REQUEST);
//            }
//            //Add tray id to the frame details from lens details
//            frameDetails.setTrayId(lensDetailsList.get(0).getTrayId());
//        }
//        response.setLensDetails(lensDetailsList);
//        response.setFrameDetails(frameDetails);
//        log.info("The lens scan payload formed is {}",response);
//        responseDTO.setData((Serializable) response);
//        responseDTO.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
//        return responseDTO;
//    }
//
//    private JITFrameResponse fillFrameDetails(OrderItemResponse orderItemResponse)throws Exception {
//        log.info("Filling frame details");
//        String userId = MDC.get("USER_ID");
//        String facilityCode = MDC.get("FACILITY_CODE");
//        JITFrameResponse jitFrameResponse = new JITFrameResponse();
//        List<PicklistOrderItem> picklistOrderItemList = picklistOrderItemRepository.findByFittingId(orderItemResponse.getFittingId());
//        if(CollectionUtils.isEmpty(picklistOrderItemList))
//            return null;
//        PicklistOrderItem picklistOrderItem =   picklistOrderItemList.stream().filter(item->item.getItemType().equalsIgnoreCase("FRAME")||item.getItemType().equalsIgnoreCase("SUNGLASS")).collect(Collectors.toList()).get(0);
//        log.info("The picklistOrderItem for frame is {}",picklistOrderItem);
//        if(picklistOrderItem==null) {//The item doesn't have the frame in picklist order item
//            log.error("The frame is not present in picklist order items");
//            throw new PickingException("The frame is not present in picklist order items",
//                    "The frame is not present in picklist order items",
//                    PickingExceptionStatus.BAD_REQUEST);
//        }
//
//
//        //Create or update the current picking summary
//        PickingSummary pickingSummary = createOrUpdateJITFramePickingSummary(picklistOrderItemList,userId ,facilityCode);
//        log.info("The picking summary is {}",pickingSummary);
//        //Find if the frame data is present in the frame details
//        PickingDetail framePickingDetail =
//                pickingDetailRepository.findByPicklistOrderItemId(picklistOrderItem.getId());
//        PickingDetail pickingDetail = createOrGetPickingDetailEntity(picklistOrderItem,framePickingDetail,pickingSummary.getId(),userId, 0 ,facilityCode);
////        pickingDetail.setJitOrder(true);
////        pickingDetail.setProductImage(picklistOrderItem.getProductImage());
////        pickingDetailRepository.save(pickingDetail);
////      Uncomment this code after confirmation of loyalty items as well
////        pickingSummary.setNoOfItems(1);
////        pickingSummaryRepository.save(pickingSummary);
//
//
//        jitFrameResponse.setOrderItemId((long)pickingDetail.getWmsOrderItemId());
//        jitFrameResponse.setProductId(pickingDetail.getProductId());
////        jitFrameResponse.setTrayId();
//        jitFrameResponse.setProductImage(pickingDetail.getProductImage());
//        jitFrameResponse.setFittingId(String.valueOf(pickingDetail.getFittingId()));
//        jitFrameResponse.setProductName(pickingDetail.getProductName());
//        if (pickingConfig.getSkippedItemRetryPick() <= pickingDetail.getSkippedCount()) {
//            jitFrameResponse.setBarcode(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
//        }else {
//            jitFrameResponse.setBarcode(pickingDetail.getItemBarcode());
//        }
//        jitFrameResponse.setLocationBarcode(pickingDetail.getLocationBarcode());
//        jitFrameResponse.setPickingSummaryId(pickingSummary.getId());
//        jitFrameResponse.setItemId(pickingDetail.getId());
//        if(LocationType.TRAY.name().equalsIgnoreCase(orderItemResponse.getLocationType())) {
//            jitFrameResponse.setCurrentLocationId(orderItemResponse.getLocationId());
//        }
//
//        return jitFrameResponse;
//    }
//
//    private LensDetails fillLensDetails(OrderItemResponse orderItemResponse, Integer orderId) {
//        log.info("Filling lens details");
//        LensDetails lensDetails = new LensDetails();
//        lensDetails.setOrderId(orderId);
//        // picklistOrderItemId;
////        lensDetails.setPickingDetailsId();
////        lensDetails.setPickingSummaryId(orderItemResponse);
//        lensDetails.setFittingId(orderItemResponse.getFittingId());
//        lensDetails.setProductId(orderItemResponse.getProduct_id());
//        lensDetails.setItemBarcode(orderItemResponse.getBarcode());
//        lensDetails.setStatus(orderItemResponse.getStatus()); //from pickingDetail
//        lensDetails.setSph(orderItemResponse.getPower().getSph());
//        lensDetails.setCyl(orderItemResponse.getPower().getCyl()); //cyl;
//        lensDetails.setAxis(orderItemResponse.getPower().getAxis());
//        lensDetails.setAp(orderItemResponse.getPower().getAp());
//        lensDetails.setLensType(ItemType.RIGHTLENS.equals(orderItemResponse.getItemType()) ? LensType.RIGHT : LensType.LEFT);
//        lensDetails.setDisplayText(orderItemResponse.getPower().getLensName());
//        lensDetails.setLocationBarcode(orderItemResponse.getLocationId());
////       locationHierarchy;
//        lensDetails.setTrayId(orderItemResponse.getLocationId());
//        return lensDetails;
//    }
//
//
//    PickingSummary createOrUpdateJITFramePickingSummary(List<PicklistOrderItem> picklistOrderItems, String userId, String facility) {
//        PickingSummary pickingSummary;
//        Optional<PickingDetail> pickingDetailOptional =
//                pickingDetailRepository.findByWmsOrderItemId(picklistOrderItems.get(0).getWmsOrderItemId());
//        if (pickingDetailOptional.isPresent()) {
//            pickingSummary =
//                    pickingSummaryService.getPickingSummary(pickingDetailOptional.get().getPickingSummaryId());
//            if (isRePickingAllow(pickingDetailOptional.get())) {
//                pickingSummary.setStatus(Constant.PICKING_STATUS.IN_PICKING);
//                pickingSummary = pickingSummaryService.savePickingSummary(pickingSummary);
//            }
//        } else {
//            pickingSummary = pickingSummaryService.savePickingSummaryNew(userId,
//                    facility, 1);
//        }
//        return pickingSummary;
//    }
//
//
//    @Override
//    public ResponseDTO completeJITPicking(JITCompleteRequest jitcompleteRequest)throws Exception {
//        ResponseDTO result = new ResponseDTO();
//        String facility = MDC.get("FACILITY_CODE");
//
//        //For Skip
//        if("Skipped".equalsIgnoreCase(jitcompleteRequest.getEvent())) {
//            if(jitcompleteRequest.getSkipReason()==null ||
//                jitcompleteRequest.getPickingDetailId() == null) {
//                log.error("The skip reason, pickingDetailID cannot be null");
//                throw new PickingException("The skip reason, pickingDetailID cannot be null",
//                        "The skip reason, pickingDetailID cannot be null",PickingExceptionStatus.BAD_REQUEST);
//            }
//            //Call skip api of the frame picking
//            PickingSummary pickingSummary = pickingSummaryService.getPickingSummary(jitcompleteRequest.getPickingSummaryId());
//            PickingDetail updatedPickingDetail =  skipJITFramePicking(jitcompleteRequest.getPickingDetailId(), jitcompleteRequest.getSkipReason(), pickingSummary);
//            if(updatedPickingDetail!=null) {
//                if(pickingSummary.getNoOfSkippedItems()==0) {
//                    pickingSummary.setNoOfSkippedItems(1);
//                }
//                pickingSummaryService.closeSummary(pickingSummary);
//            }
//        }
//
//        //For Picked
//        else if("Picked".equalsIgnoreCase(jitcompleteRequest.getEvent())) {
//            //Check if we have the required data
//            if(jitcompleteRequest.getPickingDetailId() == null ||
//            jitcompleteRequest.getPickingSummaryId() == null ||
//            jitcompleteRequest.getBarcode() == null) {
//                log.error("The barcode, pickingSummaryId, pickingDetailsId cannot be null");
//                throw new PickingException("The barcode, pickingSummaryId, pickingDetailsId cannot be null",
//                        "The barcode, pickingSummaryId, pickingDetailsId cannot be null",
//                        PickingExceptionStatus.BAD_REQUEST);
//            }
//
//            //If we have the required data scan the barcode
//            Map<String, Object> scanResult = pickingDetailService.scanItemBarcode(String.valueOf(jitcompleteRequest.getPickingDetailId()), jitcompleteRequest.getBarcode(), facility);
//            if(CollectionUtils.isEmpty(scanResult) || scanResult == null )
//            {
//                log.error("Picking could not be completed");
//                throw  new PickingException("Picking could not be completed", "Picking could not be completed", PickingExceptionStatus.BAD_REQUEST);
//            }
//            PickingSummary pickingSummary = pickingSummaryService.getPickingSummary(jitcompleteRequest.getPickingSummaryId());
//            if(pickingSummary.getNoOfPickedItems()==0) {
//                pickingSummary.setNoOfPickedItems(1);
//            }
//            pickingSummaryService.closeSummary(pickingSummary);
//        }
//
//        //For unsupported event
//        else {
//            log.error("The event type is not supported for JIT Complete");
//            throw new PickingException("The event type is not supported for JIT Complete",
//                    "The event type is not supported for JIT Complete",PickingExceptionStatus.BAD_REQUEST);
//        }
//        result.setData((Serializable) PickingConstant.SUCCESS);
//        result.setMeta(new MetaResponse("", PickingConstant.SUCCESS, PickingConstant.SUCCESS));
//        return result;
//    }
//
//    private PickingDetail skipJITFramePicking(Long pickingDetailId, String skipReason, PickingSummary pickingSummary)throws Exception {
//        PickingDetail pickingDetail = pickingDetailRepository.findById(pickingDetailId).orElse(null);
//        if(pickingDetail == null) {
//            log.error("The picking detail cannot be found for this id");
//            throw  new PickingException("The picking detail cannot be found for this id",
//                    "The picking detail cannot be found for this id", PickingExceptionStatus.BAD_REQUEST);
//        }
//        if (pickingSummary.getStatus().equalsIgnoreCase(Constant.PICKING_STATUS.CLOSED)) {
//            log.error("PickingSummary is already close for pickingDetailId{} and pickingSummaryId{} ", pickingDetailId, pickingDetail.getPickingSummaryId());
//            throw new PickingException("PickingSummary is already close for pickingDetailId: " + pickingDetailId + " and pickingSummaryId: " + pickingDetail.getPickingSummaryId(), "PickingSummary is already close", PickingExceptionStatus.BAD_REQUEST);
//        }
//
//        if (Constant.PICKING_STATUS.CANCELLED.equals(pickingDetail.getStatus())) {
//            log.info("SKIP_PICKING Unable to skip, item is cancelled for itemId: {}", pickingDetailId);
//            return pickingDetail;
//        }
//
//        if (Constant.PICKING_STATUS.REASSIGNED.equals(pickingDetail.getStatus())) {
//            log.info("SKIP_PICKING Unable to skip, item is reassigned for itemId: {}", pickingDetailId);
//            return pickingDetail;
//        }
//
//        //Skip the item
//        if (skipReason != null && skipReason.equalsIgnoreCase("Not Found")) {
//            pickingDetail.setStatus(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
//        } else {
//            pickingDetail.setStatus(Constant.PICKING_STATUS.SKIPPED);
//        }
//        if (pickingConfig.getLensPickRetryCount() > pickingDetail.getSkippedCount()) {
//                pickingDetail.setSkippedCount(pickingDetail.getSkippedCount() + 1);
//        }
//        else if (pickingConfig.getLensPickRetryCount() <= pickingDetail.getSkippedCount()) {
//                pickingDetail.setStatus(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
//        }
//        pickingDetail.setSkippedBy(MDC.get("USER_ID"));
//        pickingDetail.setSkippedDate(new Date());
//        pickingDetail.setSkippedReason(skipReason);
//        pickingDetail.setSkippedReason(Constant.PICKING_STATUS.TEMP_NOT_FOUND);
//        PickingDetail updatedPickingDetail = pickingDetailService.updateStatusPickingDetail(pickingDetail);
//
//        return updatedPickingDetail;
//    }
}

