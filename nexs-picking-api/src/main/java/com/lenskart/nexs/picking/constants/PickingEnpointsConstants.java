package com.lenskart.nexs.picking.constants;

public interface PickingEnpointsConstants {

    interface SuperVisorPanelController{
        String ADDVERB_DISCARD_ORDER = "/discard/asrs/Order/{shippingPackageId}";
        String SYNC_TO_ES_STATUS_CHANGE = "/es/change/status";
        String TEMP_SYNC_TO_MANUAL = "/temp/sync/manual";
        String GET_LISTING_PAGE_DETAILS = "/getListingDetail";

        String MARK_ITEM_PERMANENT_NOT_FOUND = "/markItemPermanentlyNotFound";
        String CREATE_SKIPPED_ITEM_SUMMARY = "/createSkippedItemSummary";
        String GET_USER_LIST = "/getUserList";
    }

    interface  JITPickingController {
        String JIT_PICKING_COMPLETE = "complete";
        String JIT_SCAN_LENS_BARCODE = "{lensBarcode}";
    }
}
