package com.lenskart.nexs.picking.response;

import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
public class ManualSyncResponse {
    private List<String> successfulShipments;
    private List<String> failedShipments;
    private Map<String, String> failureReasons;
    private Integer totalRequested;
    private Integer totalSuccessful;
    private Integer totalFailed;
    private String processedBy;
    private Instant processedAt;
    private String facility;
    private String orderState;
    private String reason;
    private String comments;
    private String message;
}
