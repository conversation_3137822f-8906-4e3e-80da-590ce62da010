package com.lenskart.nexs.picking.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.picking.dao.CacheDAO;
import com.lenskart.nexs.picking.entity.SystemPreference;
import com.lenskart.nexs.picking.exception.exception.enums.PickingExceptionStatus;
import com.lenskart.nexs.picking.repository.SystemPreferenceRepository;
import com.lenskart.nexs.picking.response.CategoryDetails;
import com.lenskart.nexs.picking.response.LocationCategoryMappingResponse;
import com.lenskart.nexs.picking.response.LocationMappingListResponse;
import com.lenskart.nexs.picking.service.SystemPreferenceServicePickingApi;
import com.lenskart.nexs.picking.constant.Constant;
import com.lenskart.nexs.picking.exception.exception.PickingException;
import com.lenskart.nexs.picking.utils.CommonUtil;
import io.micrometer.core.instrument.util.StringUtils;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SystemPreferenceServicePickingApiImpl implements SystemPreferenceServicePickingApi {

    @Autowired
    SystemPreferenceRepository systemPreferenceRepository;

    @CustomLogger
    private Logger log;

    @Autowired
    private CacheDAO cacheDAO;

    @Override
    public SystemPreference findOneByGroupAndKey(String group, String key) {
        try {
            log.info("Group {}, Key {}", group, key);
            SystemPreference systemPref = systemPreferenceRepository.findTopByGroupAndKey(group, key);
            log.info("SystemPreference {}", systemPref);
            return systemPref;
        } catch (Exception e) {
            log.error("findOneByGroupAndKey: Error{} :" + e.getMessage() + " for group {} " + group + " key {} " + key);
        }
        return null;
    }

    @Override
    public SystemPreference findOneByGroupAndKeyAndFacility(String group, String key,String facility) {
        try {
            log.info("Group {}, Key {} ,Facility {} ", group, key,facility);
            SystemPreference systemPref = systemPreferenceRepository.findTopByGroupAndKeyAndFacility(group, key,facility);
            log.info("SystemPreference {}", systemPref);
            return systemPref;
        } catch (Exception e) {
            log.error("findOneByGroupAndKey: Error{} :" + e.getMessage() + " for group {} " + group + " key {} " + key+" Facility {} "+facility);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getAllLocationCategoryMapping() {
        List<Map<String, Object>> locationMappingList = new ArrayList<Map<String, Object>>();
        Map<String, Object> locationMap = null;
        List<String> locations = getValuesAsList(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_LOCATION, Constant.SYSTEM_PREFERENCE_KEYS.WAREHOUSE_LOCATION);
        if (!CommonUtil.isBlank(locations)) {
            for (String location : locations) {
                locationMap = new HashMap<String, Object>();
                List<String> categories = getValuesAsList(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_CATEGORIES, location);
                List<String> classifications = getValuesAsList(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_CLASSIFICATION, location);
                locationMap.put("location", location);
                locationMap.put("categories", categories);
                locationMap.put("classifications", classifications);
                locationMappingList.add(locationMap);
            }
        }
        return locationMappingList;
    }

    @Override
    public List<LocationCategoryMappingResponse> getAllLocationCategoryMappingWithFacility(String facility) throws Exception {
        log.info("Inside getAllLocationCategoryMappingWithFacility ");
        List<LocationCategoryMappingResponse> locationCategoryMappingResponseList=new ArrayList<>();
        List<String> locations = getValuesAsListWithFacility(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_LOCATION_V2, Constant.SYSTEM_PREFERENCE_KEYS.WAREHOUSE_LOCATION_V2,facility);
        if (!CommonUtil.isBlank(locations)) {
            for (String location : locations) {
                List<String> categories = getValuesAsListWithFacility(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_CATEGORIES_V2, location,facility);
                List<String> classifications = getValuesAsListWithFacility(Constant.SYSTEM_PREFERENCE_GROUPS.PICKING_CLASSIFICATION_V2, location,facility);
                List<String> whPickingZone = getValuesAsListWithFacility(Constant.SYSTEM_PREFERENCE_GROUPS.WH_PICKING_ZONE, location,facility);
                LocationCategoryMappingResponse locationCategoryMappingResponse=new LocationCategoryMappingResponse();
                locationCategoryMappingResponse.setLocation(location);
                locationCategoryMappingResponse.setCategories(categories);
                locationCategoryMappingResponse.setClassifications(classifications);
                locationCategoryMappingResponse.setWhPickingZone(whPickingZone);
                locationCategoryMappingResponseList.add(locationCategoryMappingResponse);
            }
        }
        log.info("Exiting getAllLocationCategoryMappingWithFacility with response : {} ",locationCategoryMappingResponseList);
        return locationCategoryMappingResponseList;
    }

    @Override
    public List<String> getValuesAsList(String pickingChannel, String category) {
        log.info("GetValuesAsList pickingChannel {}, category {}", pickingChannel, category);
        List<String> values = new ArrayList<String>();
        SystemPreference systemPreference = findOneByGroupAndKey(pickingChannel, category);
        if (!CommonUtil.isBlank(systemPreference)) {
            if (Constant.SYSTEM_PREFERENCE_TYPES.LIST.equals(systemPreference.getType())) {
                if (null != systemPreference && null != systemPreference.getValue() && !systemPreference.getValue().isEmpty()) {
                    values = Arrays.asList(systemPreference.getValue().split("\\s*,\\s*"));
                }
            } else {
                throw new PickingException("For group -- " + pickingChannel + " and key -- " + category + " is not of type LIST. Its type is " + systemPreference.getType(), PickingExceptionStatus.NOT_FOUND);
            }
        }
        log.info("Values {}", values);
        return values;
    }


    @Override
    public List<String> getValuesAsListWithFacility(String pickingChannel, String category,String facility) throws Exception {
        log.info("getValuesAsListWithFacility pickingChannel {}, category {} ,Facility  {} ", pickingChannel, category,facility);
        List<String> values = new ArrayList<String>();
        SystemPreference systemPreference=null;
        String searchKey=pickingChannel+"_"+category+"_"+facility;
        String value=null;
        if (cacheDAO.hasKey(searchKey)) {
            log.info("[getValuesAsListWithFacility] Fetching from cache searchKey : " + searchKey);
            value = (String) cacheDAO.getVal(searchKey);
            values = new ObjectMapper().readValue(value, new TypeReference<List<String>>() {
            });
        } else {
            log.info("[getValuesAsListWithFacility] Fetching from API searchKey : " + searchKey);
            systemPreference = findOneByGroupAndKeyAndFacility(pickingChannel, category,facility);
            if (!CommonUtil.isBlank(systemPreference)) {
                if (Constant.SYSTEM_PREFERENCE_TYPES.LIST.equals(systemPreference.getType())) {
                    if (null != systemPreference && null != systemPreference.getValue() && !systemPreference.getValue().isEmpty()) {
                        values = Arrays.asList(systemPreference.getValue().split("\\s*,\\s*"));
                    }
                } else {
                    throw new PickingException("For group -- " + pickingChannel + " and key -- " + category + " is not of type LIST. Its type is " + systemPreference.getType(), PickingExceptionStatus.NOT_FOUND);
                }
            }
            value = new ObjectMapper().writeValueAsString(values);
            if (StringUtils.isNotBlank(value)) {
                log.info("[getValuesAsListWithFacility] Setting searchKey : " + searchKey + " and value : " + value);
                cacheDAO.putVal(searchKey, value,6L,TimeUnit.HOURS);
            }
        }

        log.info("Values {}", values);
        return values;
    }

    @Override
    public SystemPreference save(SystemPreference systemPreference) {
        return systemPreferenceRepository.save(systemPreference);
    }

    @Override
    public Boolean getBooleanValue(String group, String key) {
        try {
            log.info("getBooleanValue Group {}, Key {}", group, key);
            SystemPreference systemPreference = findOneByGroupAndKey(group, key);
            if (!CommonUtil.isBlank(systemPreference)) {
                if (Constant.SYSTEM_PREFERENCE_TYPES.BOOLEAN.equals(systemPreference.getType())) {
                    if (null != systemPreference && null != systemPreference.getValue() && !systemPreference.getValue().isEmpty()) {
                        return Boolean.parseBoolean(systemPreference.getValue());
                    }
                } else {
                    log.error("getBooleanValue: For group {} and key {} is not of type BOOLEAN. Its type is {}", group, key, systemPreference.getType());
                }
            }
        } catch (Exception e) {
            log.error("getBooleanValue: Error {} for group {} key {}", e.getMessage(), group, key);
        }
        return false; // Default to false if preference not found
    }

    @Override
    public Boolean getBooleanValueWithFacility(String group, String key, String facility) {
        try {
            log.info("getBooleanValueWithFacility Group {}, Key {}, Facility {}", group, key, facility);
            SystemPreference systemPreference = findOneByGroupAndKeyAndFacility(group, key, facility);
            if (!CommonUtil.isBlank(systemPreference)) {
                if (Constant.SYSTEM_PREFERENCE_TYPES.BOOLEAN.equals(systemPreference.getType())) {
                    if (null != systemPreference && null != systemPreference.getValue() && !systemPreference.getValue().isEmpty()) {
                        return Boolean.parseBoolean(systemPreference.getValue());
                    }
                } else {
                    log.error("getBooleanValueWithFacility: For group {} and key {} is not of type BOOLEAN. Its type is {}", group, key, systemPreference.getType());
                }
            }
        } catch (Exception e) {
            log.error("getBooleanValueWithFacility: Error {} for group {} key {} facility {}", e.getMessage(), group, key, facility);
        }
        return false; // Default to false if preference not found
    }
}
