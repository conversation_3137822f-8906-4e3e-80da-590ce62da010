# System Preference Management API

This API provides endpoints to manage system preferences, with specific support for controlling the partial picking feature.

## Base URL
```
/nexs/order/picking/admin
```

## Authentication
All endpoints require authentication via <PERSON><PERSON><PERSON> token in the request header.

## Endpoints

### 1. Get System Preference
**GET** `/preference/{group}/{key}`

Retrieves a system preference by group and key.

**Parameters:**
- `group` (path): The preference group
- `key` (path): The preference key

**Response:**
```json
{
  "data": {
    "id": 1,
    "group": "partial_picking",
    "key": "enable_partial_picking",
    "value": "true",
    "type": "Boolean",
    "facility": null,
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-01T10:00:00Z",
    "createdBy": "<EMAIL>",
    "updatedBy": "<EMAIL>"
  }
}
```

### 2. Get System Preference with Facility
**GET** `/preference/{group}/{key}/{facility}`

Retrieves a facility-specific system preference.

**Parameters:**
- `group` (path): The preference group
- `key` (path): The preference key
- `facility` (path): The facility code

### 3. Create or Update System Preference
**POST** `/preference`

Creates a new system preference or updates an existing one.

**Request Body:**
```json
{
  "group": "partial_picking",
  "key": "enable_partial_picking",
  "value": "true",
  "type": "Boolean",
  "facility": "LKH03",
  "createdBy": "<EMAIL>",
  "updatedBy": "<EMAIL>"
}
```

### 4. Get Partial Picking Configuration
**GET** `/partial-picking/config?facility={facility}`

Retrieves the current partial picking configuration.

**Parameters:**
- `facility` (query, optional): The facility code

**Response:**
```json
{
  "data": {
    "enabled": true,
    "facility": "LKH03",
    "message": "Partial picking configuration retrieved successfully"
  }
}
```

### 5. Update Partial Picking Configuration
**PUT** `/partial-picking/config`

Updates the partial picking configuration.

**Request Body:**
```json
{
  "enabled": true,
  "facility": "LKH03",
  "updatedBy": "<EMAIL>"
}
```

**Response:**
```json
{
  "data": {
    "enabled": true,
    "facility": "LKH03",
    "updatedBy": "<EMAIL>",
    "lastUpdated": "2024-01-01T10:00:00Z",
    "message": "Partial picking configuration updated successfully"
  },
  "message": "Partial picking configuration updated successfully"
}
```

### 6. Get Partial Picking Status
**GET** `/partial-picking/status?facility={facility}`

Returns a simple boolean indicating if partial picking is enabled.

**Parameters:**
- `facility` (query, optional): The facility code

**Response:**
```json
{
  "data": true,
  "message": "Partial picking status: enabled"
}
```

## Usage Examples

### Enable Partial Picking Globally
```bash
curl -X PUT "http://localhost:8080/nexs/order/picking/admin/partial-picking/config" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "enabled": true,
    "updatedBy": "<EMAIL>"
  }'
```

### Enable Partial Picking for Specific Facility
```bash
curl -X PUT "http://localhost:8080/nexs/order/picking/admin/partial-picking/config" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "enabled": true,
    "facility": "LKH03",
    "updatedBy": "<EMAIL>"
  }'
```

### Check Partial Picking Status
```bash
curl -X GET "http://localhost:8080/nexs/order/picking/admin/partial-picking/status?facility=LKH03" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Error Responses

### 404 Not Found
```json
{
  "error": "System preference not found for group: partial_picking and key: enable_partial_picking",
  "status": "NOT_FOUND"
}
```

### 400 Bad Request
```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "enabled",
      "message": "Enabled flag is required"
    }
  ]
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to update partial picking configuration",
  "status": "INTERNAL_SERVER_ERROR"
}
```

## Notes

1. **Facility-specific preferences**: If a facility-specific preference exists, it takes precedence over the global preference.

2. **Default behavior**: If no preference is found, the system defaults to `false` (disabled).

3. **Automatic user assignment**: If `createdBy` or `updatedBy` are not provided in requests, they will be automatically set to the authenticated user's email.

4. **Real-time effect**: Changes to the partial picking configuration take effect immediately without requiring application restart.

5. **Audit trail**: All changes are logged with timestamps and user information for audit purposes.
