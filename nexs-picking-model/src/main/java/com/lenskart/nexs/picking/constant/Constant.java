package com.lenskart.nexs.picking.constant;

import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface Constant {

    interface SYSTEM_PREFERENCE_GROUPS {
        String PICKING_CLASSIFICATION = "picking_classification";
        String PICKING_CLASSIFICATION_V2 = "picking_classification_v2";
        String PICKING_PATH = "picking_path";
        String CATALOG = "catalog";
        String PICKING_CATEGORIES = "picking_categories";
        String PICKING_CATEGORIES_V2 = "picking_categories_v2";
        String LOCATION_TYPE_PRORITY = "location_type_prority";
        String PICKING_PRIORITIES = "picking_priorities";
        String PICKING_LOCATION = "picking_location";
        String PICKING_LOCATION_V2 = "picking_location_v2";
        String PICKING_CHANNEL = "picking_channel";
        String PICKING_CHANNEL_V2 = "picking_channel_v2";
        String WH_PICKING_ZONE = "warehouse_pikcing_zone";
        String PICKING_LOCATION_TYPE_V2 = "picking_location_type_v2";
        String PICKING_DELAY_DAYS = "picking_delay_days";
        String BIN_BARCODE = "bin_barcode";
        String PICKING_SYSTEM = "picking_system";
        String LSM_COURIER = "lsm_courier";
    }

    interface SYSTEM_PREFERENCE_TYPES {
        String LIST = "LIST";
        String DATE = "Date";
        String INTEGER = "Integer";
        String BOOLEAN = "Boolean";
    }

    interface Manifest_facility {
        String DEALSKART = "O1";
        String LENSKART = "LKH03";
    }

    interface PICKING_PREFERENCE {
        String PICKING_SUMMARY = "picking_summary";
        String TYPE = "type";
        String TRACE_ITEMS_IF_LOT_SHAPE_NOT_PRESENT = "trace_items_if_lot_shape_not_present";
        String ALWAYS_TRACE_RETURNS_MASTER_SWITCH = "always_trace_returns_master_switch";
    }

    interface PICKING_CATEGORY {
        String PL = "PL";
    }

    ;

    interface PICKING_STATUS {
        String STOPPED = "STOPPED";
        String CLOSED = "CLOSED";
        String SKIPPED = "SKIPPED";
        String PICKLIST_CREATED = "PICKLIST_CREATED";
        String CREATED = "CREATED";
        String IN_PICKING = "IN_PICKING";
        String PICKED = "PICKED";
        String TRAY_MAKING = "TRAY_MAKING";
        String CANCELLED = "CANCELLED";
        String UNALLOCATED = "UN_ALLOCATED";
        String HOLDED = "HOLDED";
        String PICKED_INVALID_ITEM = "PICKED_INVALID_ITEM";
        String SHIPMENT_BREAKED = "SHIPMENT_BREAKED";
        String PICKING_USER_EMAIL = "PICKING_USER_EMAIL";
        String ORDER_CANCELED = "ORDER_CANCELED";
        String ALREADY_PICKED = "ALREADY_PICKED";
        String ASSIGNED_TO_PICKER = "ASSIGNED_TO_NEW_PICKER";
        String RESENT_FOR_PICKING = "SKIPPED_ITEM_RESENT";
        String TEMP_NOT_FOUND = "TEMP_NOT_FOUND";
        String NOT_FOUND = "NOT_FOUND";
        String REASSIGNED = "REASSIGNED";
        String INVENTORY_NOT_FOUND = "INVENTORY_NOT_FOUND";
        String DISPATCHED = "DISPATCHED";
        String DO_SKIPPED = "DO_SKIPPED";
        String PARTIAL_PICKLIST_CREATED = "PARTIALLY_CREATED";
    }

    interface PICKING_REPICK_STATUS {
        String QC_FAILED = "QC_FAILED";
    }


    interface PICKING_DEFAULT {
        Integer TOTAL_ITEM = 0;
        Integer PICKED_ITEM = 0;
        Integer SKIPPED_ITEM = 0;
        Integer SINGLE_BOX_ITEM = 0;
        Integer MULTI_BOX_ITEM = 0;
        String FITTING_REQUIRED_STATUS = "No";
        int QC_FAILED_PRIORITY = 0;
        int PINCODE_PRIORITY = 1;
        int SECONDARY_PINCODE_PRIORITY = 2;
        int BULK_ORDER_PRIORITY = 11;
        Integer MAX_UNALLOCATED_PRIORITY = 100;
    }

    interface PICKING_CONSTANT {
        String DEFAULT_KAFKA_CREATED = "KAFKA_CALL";
        String DEFAULT_REPICKING_CALL = "REPICKING_CALL";
        String LENS_INDEX_JOINING_DELIMITER = " ";
        String SUCCESS = "Success";
        String ASRS_DELIMITER = "ASRS-";
        String WMS_SOURCE = "WMS";
        String TRANSFER_SOURCE = "TRANSFER";
        String ADDVERB_CLOSE_WAVE_SCHEDULER = "ADDVERB_CLOSE_WAVE_SCHEDULER";
        String PROCESSING_TYPE_BULK = "BULK";
        String IS_BULK_ORDER = "IS_BULK_ORDER";
        String AUTOREPLENISHMENT = "AUTOREPLENISHMENT";

        String ES = "ES";

        String ASRS = "ASRS";

        String ADHOC = "ADHOC";

        String MANUAL = "Manual";

        String CREATED = "CREATED";

        String PROCESSING = "PROCESSING";

        String FR0 = "FR0";

        String FR1 = "FR1";

        String EYEFRAME = "EYEFRAME";

        String FRAME = "FRAME";

        String PRESCRIPTION_LENS = "PRESCRIPTION_LENS";

        String SUNGLASS = "SUNGLASS";

        Integer HUNDRED = 100;

        String PL = "PL";

        String FITTING_TYPE_YES = "Yes";

        String B2B_ORDER = "B2B";

        String SHIPPING_PINCODE = "SHIPPING_PINCODE";

        interface LOYALTY_TYPES {
            String GOLD = "gold";
            String GOLD_MAX = "GOLD_MAX";
        }

        String LOCATION_TYPE_PRIORITY = "LOCATION_TYPE_PRIORITY";
    }

    interface SYSTEM_PREFERENCE_KEYS {
        String CLASSIFICATION = "classification";
        String EYEFRAME = "EYEFRAME";
        String CONTACT_LENS = "CONTACT_LENS";
        String SUNGLASSES = "SUNGLASSES";
        String ORDER_WISE = "order_wise";
        String PATH_WISE = "path_wise";
        String STATUS = "status";
        String CATEGORIES = "categories";
        String PRIORITIES = "priorities";
        String WAREHOUSE_LOCATION = "warehouse_location";
        String WAREHOUSE_LOCATION_V2 = "warehouse_location_v2";
        String SINGLE_VISION = "single_vision";
        String BIFOCAL_OR_PROGRESSIVE = "bifocal_or_progressive";
        String OTHERS = "others";
        int PICKING_ITEM_COUNT = 50;
        String BYPASS = "bypass";
        String PICKING_ENABLED_BEFORE = "PICKING_ENABLED_BEFORE";
        String PICKING_USER_EMAIL = "PICKING_USER_EMAIL";
        String TIME_LAG = "TIME_LAG";
    }

    interface FETCH_BARCODE_DETAIL {
        Condition condition = Condition.GOOD;
        Status status = Status.AVAILABLE;
        Availability availability = Availability.AVAILABLE;
    }

    interface CACHE_INFO {
        String CATALOG_PRODUCT_RESPONSE_CACHE_KEY = "NEXS_PICKING_CATALOG_PRODUCT_";
        String LOCATION_HIERARCHY_RESPONSE_CACHE_KEY = "NEXS_PICKING_LOCATION_HIERARCHY_";
        Long cacheTime = 24L;
    }

    interface PICKING_ORDER_ITEM_STATUS {
        Integer CREATED = 0;
        Integer READY_TO_BE_PICKED = 1;
    }

    interface JSON_CONSTANT {
        String PICKING_SUMMARY = "PICKINGSUMMARY";
        String USER_PREFERENCES = "USERPREFERENCES";
        String ROW_UPDATED = "rowUpdated";
        String SUCCESSFUL = "successful";
        String PICKLIST = "picklist";
        String CODE = "code";
        String ID = "id";
        String CREATED = "created";
        String PICKLIST_ITEMS = "picklistItems";
        String DISPLAY_ORDER_CODE = "displayOrderCode";
        String ITEM_SKU = "itemSku";
        String SALE_ORDER_ITEM_ID = "saleOrderItemId";
        String SALE_ORDER_CODE = "saleOrderCode";
        String SHIPPING_PACKAGE_CODE = "shippingPackageCode";
        String SALE_ORDER_ITEM_CODE = "saleOrderItemCode";
        String ITEM_NAME = "itemName";
        String ERRORS = "errors";
        String DESCRIPTION = "description";
        String STYLE = "style";
        String COLOR = "color";
        String IMAGE = "image";
        String PRODUCT_IMAGE = "productImage";
        String PUTAWAYDTO = "putawayDTO";
        String PUTAWAYITEMDTOS = "putawayItemDTOs";
        String RESHIPMENT_SALE_CODE = "reshipmentSaleOrderCode";
        String TRUE = "true";
        String ASSIGN_B2B_COURIER = "assignB2BCourier";
        String SHIPPING_PACKAGE_DTO = "shippingPackageFullDTO";
        String DEFAULT_INTERNATIONAL_CARRIER = "DHL";
        String INDIA_COUNTRY_ID = "IN";
        String COURIER_DISABLING_DELAY_KEY = "PINCODE_DISABLE_DELAY";
        Integer DEFAULT_COURIER_DISABLE_DELAY = 10;
    }

    interface CLASSIFIACTION {
        Integer EYEFRAME_ID = 11355;
        Integer SUNGLASS_ID = 11357;
        String EYEFRAME = "EYEFRAME";
        String SUNGLASS = "SUNGLASS";
        Integer PRESCRIPTION_LENS = 11356;
        Integer CONTACT_LENS_ID = 11354;
        String CONTACT_LENS = "CONTACT_LENS";
        String CONTACT_LENS_SOLUTION = "CONTACT_LENS_SOLUTION";
        String CL_AND_CL_SOLUTION = "CONTACT_LENS+++CONTACT_LENS_SOLUTION";
        Integer LIQUID_ClASSIFICATION_1 = 18585;
        Integer LIQUID_ClASSIFICATION_2 = 19153;
    }

    interface PICKLIST_ORDER_STATUS {
        Integer CREATED = 0;
        Integer ES_SYNC = 1;
        Integer PICKING_DETAIL_CREATED = 2;
        Integer ADVERB_ORDER = 3;
        Integer ADVERB_DISCARD_ES_SYNC = 4;
    }

    interface PICKLIST_SUMMARY_TYPE {
        int APP_USER = 1;
        int ASRS_USER = 3;
        int SUPER_ORDER_USER = 4;
        int BULK_SUMMARY = 5;
        int DISTRIBUTED_ORDER = 6;
    }

    Map<Integer, List<Integer>> ALLOWED_STATUS_MAP = new HashMap<Integer, List<Integer>>() {
        {
            put(1, Arrays.asList(4));
        }
    };

    interface UNICOM_BARCODE_STATUS {
        String GOOD_INVENTORY = "GOOD_INVENTORY";
        String BAD_INVENTORY = "BAD_INVENTORY";
        String ALLOCATED = "ALLOCATED";
        String LIQUIDATED = "LIQUIDATED";
    }

    public interface ORDER_STATE {
        String CANCELLED = "CANCELLED";
        String PROCESSING = "processing";
        String COMPLETE = "complete";
        String HOLDED = "holded";
        String CLOSED = "closed";
    }

    public interface IMS_WMS_SYNC_ERROR {
        String COMMON = "Ims Wms sync call failed. ";
        String CANCELLED = "Unable to pick order item. Order status is CANCELLED";
        String QC_HOLD = "Unable to pick order item. Order status is QC_HOLD";
        String QC_DONE = "Unable to pick order item. Order status is QC_DONE";
        String IN_QC = "Unable to pick order item. Order status is IN_QC";
        String PICKED = "Unable to pick order item. Order status is PICKED";
        String AWB_CREATED = "Unable to pick order item. Order status is AWB_CREATED";
        String REASSIGNED = "Unable to pick order item. Order status is REASSIGNED";
        String POWER_CHANGED = "Unable to pick order item. Order status is POWER_CHANGED";
        String INVOICED = "Unable to pick order item. Order status is INVOICED";
        String DISPATCHED = "Unable to pick order item. Order status is DISPATCHED";
        String COMPLETE = "Unable to pick order item. Order status is COMPLETE";
        String HOLD = "Item on Hold, Proceed to Pick Next Item";
        String IMS_UPDATE_FAIL = "IMS update failed for request";
    }

    public interface ERROR_MSG {
        String NEXST_DAY_DELIVERY_ORDER_REASSIGNMENT = "NDD orders not started picking in 30 minutes";
    }

    interface SUPERVISOR_PANEL_FIELDS {
        List<String> SEARCH_FIELDS = Arrays.asList("location_barcode", "product_id", "increment_id", "channel", "skipped_by");
        List<String> SORT_FIELDS = Arrays.asList("location_barcode", "product_id", "increment_id", "channel", "skipped_date", "skipped_by");
    }

    interface ADDVERB_CONSTANT{
        String INSTANCE_CODE = "lk_bhi";
        String USER = "AT_WES";
        int TOTAL_HOURS = 24;
        Long HOURS_TO_MILLISECOND_FACTOR = 3600000l;
        int DEFAULT_CUTOFF_HOURS = 3;
        int PICKING_CUTOFF_ADDITION_FACTOR = 1;
    }

    interface PICKING_ORDER_TYPE_CONSTANT {
        String STANDARD="standard";
        String STANDARDUPPERCASE="STANDARD";
        String STANDARDCAMELCASE="Standard";
        String SUPER_ORDER="SUPER_ORDER";
        String DISTRIBUTED_ORDERS = "DISTRIBUTED_ORDERS";
        String DO_SO_ORDERS = "DO_SO_ORDERS";
    }

    String INWARD_SO_ITEM_OPERATION = "INWARD_SO_ITEM";
    String STOCK_TRANSFER_OPERATION = "STOCK_TRANSFER";
    String INWARD_REQUEST_CURRENCY = "INR";
    String INWARD_REQUEST_LEGAL_OWNER = "LKIN";

    String MARK_INVENTORY_FOUND_SHELF = "DEFAULT";
    String MARK_INVENTORY_FOUND_QTY = "1";

    interface CREATE_TRANSFER{
        String SOURCE_FACILITY = "DK02";
        String DEST_FACILITY = "NXS2";
        String SUPER_ORDER_TYPE = "SUPER_ORDER";
    }

    interface DISTRIBUTOR_ORDER {
        String DO_SKIP_REASON = "DO_SKIP";
        String UFF_UPDATES = "UffUpdates";
    }

    interface PICKING_ORDER_SUMMARY_TYPE_CONSTANT {
        String STANDARD="standard_SUMMARY";
        String SUPER_ORDER="SUPER_ORDER_SUMMARY";
        String DISTRIBUTED_ORDERS = "DISTRIBUTED_ORDERS_SUMMARY";
        String DO_SO_ORDERS = "DO_SO_ORDERS_SUMMARY";
    }
    interface MDC_CONSTANTS{
        String FACILITY_CODE = "FACILITY_CODE";
        String USER_ID = "USER_ID";
    }

    interface PICKLIST_ORDER_META {
        String COUNRY_CODE = "country_code";
        String TLP_UNFF_ORDER = "TLP_UNFF_ORDER";
        String LO_ORDER = "LO_ORDER";
        String STORE_INVENTORY_TYPE = "STORE_INVENTORY_TYPE";
        String TLP_ORDER = "TLP_ORDER";
        String LP_ORDER = "LP_ORDER";
        String LP = "LP";
        String LO = "LO";
        String TLP = "TLP";
        String FULFILLABLE_TYPE = "FULFILLABLE_TYPE";

    }
}
