package com.lenskart.nexs.picking.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.Logging;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.ims.request.BarcodeItemRequest;
import com.lenskart.nexs.ims.request.FetchStockDetailsRequest;
import com.lenskart.nexs.ims.response.FetchStockDetailsResponse;
import com.lenskart.nexs.picking.config.AddverbSlaveMigrationConfig;
import com.lenskart.nexs.picking.config.PickingConfig;
import com.lenskart.nexs.picking.connector.AsrsConnector;
import com.lenskart.nexs.picking.connector.IMSConnector;
import com.lenskart.nexs.picking.constant.Constant;
import com.lenskart.nexs.picking.dto.AddverbWaveRequestDto;
import com.lenskart.nexs.picking.dto.AdverbWaveFormationRequiredDetails;
import com.lenskart.nexs.picking.entity.AddverbWaveRequestEntity;
import com.lenskart.nexs.picking.entity.PickingDetail;
import com.lenskart.nexs.picking.entity.PickingSummary;
import com.lenskart.nexs.picking.entity.PicklistOrderItem;
import com.lenskart.nexs.picking.entity.PicklistOrderItemMetaData;
import com.lenskart.nexs.picking.entity.WaveDetailEntity;
import com.lenskart.nexs.picking.enums.GroupType;
import com.lenskart.nexs.picking.enums.OrderEventType;
import com.lenskart.nexs.picking.enums.WaveOrderType;
import com.lenskart.nexs.picking.enums.WaveStatus;
import com.lenskart.nexs.picking.enums.WaveSyncType;
import com.lenskart.nexs.picking.esDocument.PicklistOrderItemDocument;
import com.lenskart.nexs.picking.exception.exception.PickingException;
import com.lenskart.nexs.picking.payload.addverb.WaveMessage;
import com.lenskart.nexs.picking.payload.addverb.WavePickingItem;
import com.lenskart.nexs.picking.repository.writeonly.AdverbWaveRequestRepository;
import com.lenskart.nexs.picking.repository.writeonly.PickingDetailRepository;
import com.lenskart.nexs.picking.repository.writeonly.PickingSummaryRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemDocumentRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemMetaDataRepository;
import com.lenskart.nexs.picking.repository.writeonly.PicklistOrderItemRepository;
import com.lenskart.nexs.picking.repository.writeonly.WaveDetailRepository;
import com.lenskart.nexs.picking.request.AsrsSkuRequest;
import com.lenskart.nexs.picking.request.SkuRequest;
import com.lenskart.nexs.picking.response.AdverbWaveResponse;
import com.lenskart.nexs.picking.response.AsrsSkuResponse;
import com.lenskart.nexs.picking.response.AsrsSkuResponseList;
import com.lenskart.nexs.picking.response.WaveDetailItem;
import com.lenskart.nexs.service.RedisHandler;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class AddverbUtils {

    @CustomLogger
    private Logger log;

    @Autowired
    PicklistOrderItemDocumentRepository picklistOrderItemDocumentRepository;

    @Autowired
    PickingDetailRepository pickingDetailRepository;

    @Autowired
    PicklistOrderItemRepository picklistOrderItemRepository;

    @Autowired
    AddverbSlaveMigrationConfig addverbSlaveMigrationConfig;


    @Autowired
    WaveDetailRepository waveDetailRepository;

    @Autowired
    PicklistOrderItemMetaDataRepository picklistOrderItemMetaDataRepository;

    @Value("${jpa.pagination.pagesize}")
    int pageSize;

    @Value("${bhivadi.nexs.facility}")
    String nexsBhivadiFacility;

    @Value("${wave.details.created.at.interval.minutes}")
    Integer waveDetailsCreatedAtIntervalMinutes;

    @Value("${is.asrs.inventory.check.allowed}")
    boolean isASRSInventoryCheckAllowed;
    @Value("${is.asrs.inventory.check.v2.allowed}")
    boolean isASRSInventoryCheckV2Allowed;

    @Value("${recon.pid.redis.ttl}")
    private String redisTtl;

    @Value("${nexs.partial.picking.sync.enable}")
    private boolean enablePartialPicking;

    @Value("#{'${list.wave.type.for.partial.sync:FR1_V3}'.split(',')}")
    Set<String> allowedWaveTypeForPartial;

    @Value("#{'${picking.asrs.blocked.pid.sync:}'.split(',')}")
    List<Integer> asrsBlockedPidList;

    @Autowired
    private PickingConfig pickingConfig;

    @Autowired
    private  PickingCommonUtils pickingCommonUtils;

    @Autowired
    private AdverbWaveRequestRepository adverbWaveRequestRepository;
    ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private PickingSummaryRepository pickingSummaryRepository;
    @Autowired
    private WavePayloadUtil wavePayloadUtil;


    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private IMSConnector imsConnector;

    @Autowired
    private AsrsConnector asrsConnector;

    private static final String PID_RECONCILED = "PID_RECONCILED_";


    public boolean noEntryFoundOnPickingDetail(String shipmentId) {
        return CollectionUtils.isEmpty(pickingDetailRepository.findByShipmentId(shipmentId));
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Logging
    public WaveMessage updatePickingDetailAndCreateWaveDetail(AdverbWaveFormationRequiredDetails adverbWaveFormationRequiredDetails) throws Exception {
        List<PicklistOrderItem> picklistOrderItemList = adverbWaveFormationRequiredDetails.getPicklistOrderItemList();
        PicklistOrderItem picklistOrderItem =  picklistOrderItemList.get(0);
        WaveMessage waveMessage = null;
        boolean isAsrsPidCheckBlocked = !CollectionUtils.isEmpty(picklistOrderItemList)
                && picklistOrderItemList.stream().anyMatch(item -> asrsBlockedPidList.contains(item.getProductId()));
        if(isAsrsPidCheckBlocked){
            log.info("picklistOrderItem : {} shipment belong to blocked pid",picklistOrderItem.getShipmentId());
            return null;
        }
        /** Setting isInventoryAvailable to true as default.
         * This is to allow waveMessage to be created for power_change event
         * */
        boolean isInventoryAvailable = true;
        log.info("[AddverbUtils ,updatePickingDetailAndCreateWaveDetail] shipmentId : {} has the request as {}",picklistOrderItem.getShipmentId(),adverbWaveFormationRequiredDetails);
        if(!OrderEventType.POWER_CHANGED.equals(adverbWaveFormationRequiredDetails.getEventType())) {
            isInventoryAvailable = checkIfInventoryIsAvailableForAsrsSync(picklistOrderItemList, adverbWaveFormationRequiredDetails.getEventType(), adverbWaveFormationRequiredDetails.getAsrsInventoryCache());
        }

        log.info("isInventoryAvailable: {} | for shipmentId:{}",isInventoryAvailable,picklistOrderItem.getShipmentId());
        if(isInventoryAvailable) {
            waveMessage = createWaveMessageAndUpdatePickingData(adverbWaveFormationRequiredDetails);
        }
        return waveMessage;
    }

    private WaveMessage createWaveMessageAndUpdatePickingData(AdverbWaveFormationRequiredDetails adverbWaveFormationRequiredDetails) throws Exception {
        WaveMessage waveMessage;
        List<PicklistOrderItem> picklistOrderItemList = adverbWaveFormationRequiredDetails.getPicklistOrderItemList();
        PicklistOrderItem picklistOrderItem = picklistOrderItemList.get(0);
        PickingSummary pickingSummary = adverbWaveFormationRequiredDetails.getPickingSummary();
        long epochTimeMills = adverbWaveFormationRequiredDetails.getEpochTimeMills();
        OrderEventType eventType = adverbWaveFormationRequiredDetails.getEventType();
        GroupType groupType = adverbWaveFormationRequiredDetails.getGroupType();
        List<PicklistOrderItemDocument> picklistDocument = null;
        if(enablePartialPicking && OrderEventType.PARTIAL_PICK.equals(eventType)) {
              picklistDocument = picklistOrderItemDocumentRepository.findByPicklistOrderItemIdIn(
                    picklistOrderItemList.stream()
                            .map(PicklistOrderItem::getId)
                            .collect(Collectors.toList())
            );
        }else {
            picklistDocument = picklistOrderItemDocumentRepository.findByShipmentId(picklistOrderItem.getShipmentId());
        }
        String groupId = GroupType.SHIPMENT_TYPE.equals(groupType) ? picklistOrderItem.getShipmentId() : String.valueOf(picklistOrderItem.getFittingId());

        List<PickingDetail> pickingDetailList = wavePayloadUtil.createUpdatePickListAndPickingDetail(pickingSummary, picklistOrderItemList, picklistDocument, groupId, eventType);
        log.info("[AddverbUtils, updatePickingDetailAndCreateWaveDetail] The pickingDetails fetched is " + pickingDetailList);

        // Find/Create necessary details for creating wave
        WavePickingItem wavePayload = wavePayloadUtil.generateWavePayloadUsingPickListOrderItemsAndPickingDetails(picklistOrderItemList, groupId, eventType, pickingSummary, groupType, pickingDetailList);
        //wavePayload.setPartialPickingFlag(OrderEventType.PARTIAL_ASRS.equals(eventType) ? WaveSyncType.PARTIAL.name() : WaveSyncType.COMPLETE.name());

        String idempotencyKey = groupId+"-"+wavePayload.getWaveId()+"-"+wavePayload.getOrderStatus();
        WaveOrderType waveOrderType = wavePayloadUtil.findWaveOrderType(picklistOrderItemList);

        WaveDetailEntity waveDetail = createWave(nexsBhivadiFacility, epochTimeMills, pickingSummary,
                groupType, eventType, groupId, idempotencyKey, wavePayload, waveOrderType);

        waveMessage = new WaveMessage(waveDetail.getId(), pickingSummary.getId(),
                waveDetail.getGroupId());
        return waveMessage;
    }

    /**
     * @param picklistOrderItemList
     * @param eventType
     * @param asrsInventoryCache
     * @return
     */
    private boolean checkIfInventoryIsAvailableForAsrsSync(List<PicklistOrderItem> picklistOrderItemList, OrderEventType eventType, Map<Integer, Integer> asrsInventoryCache) {
        //Skip inventory check for POWER_CHANGED Operation
        boolean isInventoryAvailable = true;
            log.info("[AddverbUtils, updatePickingDetailAndCreateWaveDetail] isAsrsInventoryCheck Allowed: {}",isASRSInventoryCheckAllowed);
            if(isASRSInventoryCheckAllowed){
                if(isASRSInventoryCheckV2Allowed) {
                    log.info("[{}, updatePickingDetailAndCreateWaveDetail] Cheking asrs inventory with cache for picklistOrderItemList {}",this.getClass().getSimpleName(), picklistOrderItemList);
                    isInventoryAvailable = checkIsAsrsInventoryAvailableWithCache(picklistOrderItemList, asrsInventoryCache);
                }else {
                    log.info("[{}, updatePickingDetailAndCreateWaveDetail] Cheking asrs inventory using direct api call for picklistOrderItemList {}",this.getClass().getSimpleName(), picklistOrderItemList);
                    isInventoryAvailable = checkIsAsrsInventoryAvailable(picklistOrderItemList);
                }
                try {
                    if (!isInventoryAvailable) {
                        List<Integer> pidList = picklistOrderItemList.stream().map(PicklistOrderItem::getProductId).collect(Collectors.toList());
                        log.info("[updatePickingDetailAndCreateWaveDetail] pid list for recon {}", pidList);
                        pushScriptMessage(pidList);
                    }
                } catch (Exception e) {
                    log.error("[updatePickingDetailAndCreateWaveDetail] Error while pushing pid to script for recon" + e);
                }
            } else {
                isInventoryAvailable = isInventoryAvailable(picklistOrderItemList, true);
            }
        return isInventoryAvailable;
    }

    @Retryable(value = Exception.class, maxAttempts = 3)
    public void pushScriptMessage(List<Integer> pidList) throws Exception {
        List<Integer> nonReconciledPidList = new ArrayList<>();
        for (Integer pid : pidList) {
            Object pidObject = RedisHandler.redisOps(RedisOps.GET, PID_RECONCILED + pid);
            if (Objects.isNull(pidObject)) {
                nonReconciledPidList.add(pid);
                RedisHandler.redisOps(RedisOps.SETVALUETTL, PID_RECONCILED + pid, true, Long.valueOf(redisTtl), TimeUnit.MINUTES);
            }
        }
        if (nonReconciledPidList.size() > 0) {
            kafkaTemplate.send(
                    pickingConfig.getPickingScriptPidReconTopic(),
                    String.valueOf(pidList.get(0)),
                    ObjectHelper.writeValue(nonReconciledPidList)
            ).get();
            log.info("Posting to kafka topic :" + pickingConfig.getPickingScriptPidReconTopic());
        }
    }

    public boolean checkIsAsrsInventoryAvailable(List<PicklistOrderItem> picklistOrderItemList) {
        try {
            List<Integer> pidList = picklistOrderItemList.stream().map(item -> item.getProductId()).collect(Collectors.toList());
            log.info("[AddverbUtils, checkIsInventoryAvailable] The pidList formed is {} for the picklistOrderItemList {}",pidList,picklistOrderItemList);
            AsrsSkuRequest asrsSkuRequestList = createAsrsInventoryCheckPayloadUsingPids(pidList);
            AsrsSkuResponseList asrsInventoryCountResponses = asrsConnector.getAsrsInventoryCount(asrsSkuRequestList);
             log.info("[AddverbUtils, checkIsInventoryAvailable] The asrs inventory found is {} for the picklistOrderItemList {}",asrsInventoryCountResponses,picklistOrderItemList);
            boolean isAllAsrsInventoryAvailable = validateAsrsInventory(asrsInventoryCountResponses.getSkuList(), pidList);
            log.info("[AddverbUtils, checkIsInventoryAvailable] isAllAsrsInventoryFound for the picklistOrderItemList {}",isAllAsrsInventoryAvailable,picklistOrderItemList);
            return isAllAsrsInventoryAvailable;
        }catch (Exception e) {
            log.error("[AddverbUtils, checkIsInventoryAvailable] Unable to check ASRS inventory for picklistOrderItemList {} due to exception {}",picklistOrderItemList,e);
            return false;
        }
    }

    public boolean checkIsAsrsInventoryAvailableWithCache(List<PicklistOrderItem> picklistOrderItemList, Map<Integer, Integer> asrsInventoryCache) {
        try {
            log.info("[{}, checkIsAsrsInventoryAvailableWithCache] The map formed is {} with thread Id {}",this.getClass().getSimpleName(),asrsInventoryCache,Thread.currentThread().getId());
            List<Integer> pidList = picklistOrderItemList.stream()
                .map(PicklistOrderItem::getProductId)
                .collect(Collectors.toList());
            
            log.info("[AddverbUtils, checkIsAsrsInventoryAvailableWithCache] The pidList formed is {} for the picklistOrderItemList {}", 
                pidList, picklistOrderItemList);

            // Filter out PIDs that are already in cache
            List<Integer> uncachedPids = pidList.stream()
                .filter(pid -> !asrsInventoryCache.containsKey(pid))
                .collect(Collectors.toList());

            if (!uncachedPids.isEmpty()) {
                AsrsSkuRequest asrsSkuRequestList = createAsrsInventoryCheckPayloadUsingPids(uncachedPids);
                AsrsSkuResponseList asrsInventoryCountResponses = asrsConnector.getAsrsInventoryCount(asrsSkuRequestList);
                log.info("[AddverbUtils, checkIsAsrsInventoryAvailableWithCache] The asrs inventory found is {} for the uncached PIDs {}", 
                    asrsInventoryCountResponses, uncachedPids);

                // Update cache with new responses
                if (asrsInventoryCountResponses != null && asrsInventoryCountResponses.getSkuList() != null) {
                    for (AsrsSkuResponse response : asrsInventoryCountResponses.getSkuList()) {
                        try {
                            Integer pid = Integer.parseInt(response.getSku());
                            Integer count = response.getSkuCount();
                            asrsInventoryCache.put(pid, count);
                            log.info("[{}, checkIsAsrsInventoryAvailableWithCache] The map updated is {} with thread Id {}",this.getClass().getSimpleName(),asrsInventoryCache,Thread.currentThread().getId());
                        } catch (NumberFormatException e) {
                            log.error("[AddverbUtils, checkIsAsrsInventoryAvailableWithCache] Invalid SKU format: {}", response.getSku());
                        }
                    }
                }
            }

            boolean isAllAsrsInventoryAvailable = validateAsrsInventoryWithCache(pidList, asrsInventoryCache);
            log.info("[AddverbUtils, checkIsAsrsInventoryAvailableWithCache] isAllAsrsInventoryFound for the picklistOrderItemList {}", 
                isAllAsrsInventoryAvailable);
            if(isAllAsrsInventoryAvailable) {
                reduceAsrsResponseQty(pidList, asrsInventoryCache);
            }
            return isAllAsrsInventoryAvailable;
            
        } catch (Exception e) {
            log.error("[AddverbUtils, checkIsAsrsInventoryAvailableWithCache] Unable to check ASRS inventory for picklistOrderItemList {} due to exception {}", 
                picklistOrderItemList, e);
            return false;
        }
    }

    private void reduceAsrsResponseQty(List<Integer> pidList, Map<Integer, Integer> asrsInventoryCache) {
        Map<Integer, Integer> pidAndCountMap = pidList.stream()
                .collect(Collectors.toMap(pid -> pid, count -> 1, Integer::sum));

        for (Integer pid : pidAndCountMap.keySet()) {
            Integer asrsCount = asrsInventoryCache.get(pid);
            Integer requiredCount = pidAndCountMap.get(pid);
            Integer availableCount = asrsCount;
            asrsInventoryCache.put(pid, availableCount==null?0:availableCount - requiredCount);
        }
        log.info("[{}, reduceAsrsResponseQty] The new asrs inventory map formed for thread Id {} is {}",this.getClass().getSimpleName(),Thread.currentThread().getId(),asrsInventoryCache);
    }

    private boolean validateAsrsInventory(List<AsrsSkuResponse> asrsInventoryCountResponses, List<Integer> pidList)throws Exception {
        boolean isAllPidInventoryAvaialble = true;
        Map<Integer, Integer> pidAndCountMap = pidList.stream().collect(Collectors.toMap(pid->pid, (count)->1, Integer::sum));
        for(Integer pid: pidAndCountMap.keySet()) {

            AsrsSkuResponse asrsSkuResponse = asrsInventoryCountResponses.stream().filter(item -> Integer.toString(pid).equalsIgnoreCase(item.getSku())).
                    findFirst().orElse(null);
            if(asrsSkuResponse==null) {
                     isAllPidInventoryAvaialble = false;
                     break;
            }
            Integer requiredCount = pidAndCountMap.get(pid);
            Integer availableCount = asrsSkuResponse.getSkuCount();
            if(requiredCount>availableCount) {
                log.info("[AddverbUtils, validateAsrsInventory] The requiredCount {} is greater than available count {} for pid {}",requiredCount,availableCount,pid);
                isAllPidInventoryAvaialble = false;
                break;
            }


        }
        return isAllPidInventoryAvaialble;
    }

    private boolean validateAsrsInventoryWithCache(List<Integer> pidList, Map<Integer, Integer> asrsInventoryCache) {
        Map<Integer, Integer> pidAndCountMap = pidList.stream()
            .collect(Collectors.toMap(pid -> pid, count -> 1, Integer::sum));

        for (Integer pid : pidAndCountMap.keySet()) {
            Integer asrsCount = asrsInventoryCache.get(pid);
            if (asrsCount == null) {
                log.info("[AddverbUtils, validateAsrsInventoryWithCache] No inventory data found in cache for pid {}", pid);
                return false;
            }

            Integer requiredCount = pidAndCountMap.get(pid);
            Integer availableCount = asrsCount;
            if (requiredCount > availableCount) {
                log.info("[AddverbUtils, validateAsrsInventoryWithCache] The requiredCount {} is greater than available count {} for pid {}", 
                    requiredCount, availableCount, pid);
                return false;
            }
        }
        return true;
    }

    public AsrsSkuRequest createAsrsInventoryCheckPayloadUsingPids(List<Integer> pidList) {
        Set<Integer> pidSet = pidList.stream().collect(Collectors.toSet());
        AsrsSkuRequest asrsSkuRequestList = new AsrsSkuRequest();
        List<SkuRequest> skuList = new ArrayList<>();
        for(Integer pid:pidSet) {
                SkuRequest skuRequest = new SkuRequest();
                skuRequest.setSku(Integer.toString(pid));
                skuList.add(skuRequest);
        }
        asrsSkuRequestList.setSkuList(skuList);
        log.info("[AddverbUtils, createAsrsInventoryCheckPayloadUsingPids] The asrsSkuRequestList formed is {}",asrsSkuRequestList);
        return asrsSkuRequestList;
    }

    @Retryable(value = Exception.class, maxAttempts = 3)
    public void publishToKafka(WaveMessage waveMessage) {
        Message<String> message = MessageBuilder
                .withPayload(ObjectHelper.writeValue(waveMessage))
                .setHeader(KafkaHeaders.TOPIC, pickingConfig.getAdverbCreateWaveTopicName())
                .setHeader(KafkaHeaders.MESSAGE_KEY, waveMessage.getGroupId())
                .build();
        log.info("Posting to kafka topic :" + pickingConfig.getAdverbCreateWaveTopicName() + " msg:" + message);
        kafkaTemplate.send(message);
    }


    public long getEpochTimeMills() {
        return Instant.now().toEpochMilli();
    }


//    public List<PickingDetail> createUpdatePickListAndPickingDetail(PickingSummary pickingSummary,
//                                                                    List<PicklistOrderItem> picklistOrderItems,
//                                                                    List<PicklistOrderItemDocument> picklistDocument) throws Exception {
//        //ToDo: Make it return pickingDetails
//        List<PickingDetail> pickingDetailList = new ArrayList<>();
//        for (PicklistOrderItem pickListOrderItem :picklistOrderItems) {
//            PickingDetail pickingDetail =
//                    pickingDetailRepository.findByWmsOrderItemId(pickListOrderItem.getWmsOrderItemId()).orElse(null);
//            if (pickingDetail == null) {
//                createNewPickingDetail(pickingSummary.getAssignedTo(), pickingSummary.getId(), pickingDetailList, pickListOrderItem);
//            } else {
//                log.info("Picking detail is already created for this itemId: " + pickListOrderItem.getWmsOrderItemId());
//                updatePickingDetailObject(pickingSummary.getAssignedTo(), pickingSummary.getId(),
//                        pickingDetailList, pickListOrderItem, pickingDetail);
//            }
//            pickListOrderItem.setStatus(Constant.PICKLIST_ORDER_STATUS.ADVERB_ORDER);
//        }
//        picklistOrderItemRepository.saveAll(picklistOrderItems);
//        pickingDetailRepository.saveAll(pickingDetailList);
//
//        //Delete from ES
//        picklistOrderItemDocumentRepository.deleteAll(picklistDocument);
//        log.info("ES document deleted: {}",ObjectHelper.convertToString(picklistDocument));
//        return pickingDetailList;
//    }

    //ToDO: CleanUp: Remove if FR1V1 is removed
    public void addItemToShipmetMap(Map<String, List<PicklistOrderItem>> picklistOrderItemMap,
                                     PicklistOrderItem pickList) {
        String key = pickList.getShipmentId();
        log.info("[AddverbUtils, addItemToShipmentMap] The actual set is "+picklistOrderItemMap.get(key).toString());

        boolean noneMatch = picklistOrderItemMap.get(key).stream().noneMatch(obj -> Comparator.comparing(PicklistOrderItem::getId).compare(pickList,obj)==0);
        log.info("Is pickListOrderItemID {} missing in the map {} is {} ",pickList.getId(),picklistOrderItemMap.get(key),noneMatch);
        if(noneMatch) {
            picklistOrderItemMap.get(key).add(pickList);
        }
        log.info("[AddverbUtils, addItemToShipmentMap] The new set formed is "+picklistOrderItemMap.get(key).toString());
    }


    public void addItemToShipmetMap(Map<String, List<PicklistOrderItem>> picklistOrderItemMap,
                                    PicklistOrderItem pickList, String key) {
//        Comparator<PicklistOrderItem> picklistOrderItemComparator = Comparator.comparing(PicklistOrderItem::getId);
        log.info("[AddverbUtils, addItemToShipmentMap] The actual set is "+picklistOrderItemMap.get(key).toString());
        boolean noneMatch = picklistOrderItemMap.get(key).stream().noneMatch(obj -> Comparator.comparing(PicklistOrderItem::getId).compare(pickList,obj)==0);
        log.info("Is pickListOrderItemID {} missing in the map {} is {}",pickList.getId(),picklistOrderItemMap.get(key),noneMatch);
       if(noneMatch) {
           picklistOrderItemMap.get(key).add(pickList);
       }
        log.info("[AddverbUtils, addItemToShipmentMap] The new set formed is "+picklistOrderItemMap.get(key).toString());
    }
//    private boolean checkIfPickListAdditionisAllowed(Map<String, List<PicklistOrderItem>> picklistOrderItemMap, PicklistOrderItem pickList, String key) {
//        List<PicklistOrderItem> alreadyInMapPicklistOrderItem =  picklistOrderItemMap.get(key);
//        log.info("[AddverbUtils, checkIfPickListAdditionisAllowed] Already in picklistOrderItem Map {}",alreadyInMapPicklistOrderItem);
//        //Checking using id cause id is a unique field
//        boolean isPickListInMap = alreadyInMapPicklistOrderItem.stream().anyMatch(picklistOrderItem -> picklistOrderItem.getId() == pickList.getId());
//        //Return not of the result cause if it is in map it will not be added else added
//        return !isPickListInMap;
//    }

    public String buildAndGetKey(PicklistOrderItem pickList, GroupType groupType) {
        StringBuilder stringBuilder = new StringBuilder();
        if(GroupType.FITTING_TYPE == groupType) {
            stringBuilder.append(pickList.getFittingId());
        } else {
            stringBuilder = new StringBuilder(pickList.getShipmentId());
        }
        if (Objects.nonNull(pickList.getRepickStatus())) {
            stringBuilder.append(":").append(pickList.getRepickStatus());
        }
        return stringBuilder.toString();
    }


//    private void updatePickingDetailObject(String assignedTo, Long pickingSummaryId, List<PickingDetail> pickingDetailList,
//                                           PicklistOrderItem picklistOrderItem, PickingDetail alreadyExistPickingDetail) {
//        alreadyExistPickingDetail.setStatus(Constant.PICKING_STATUS.PICKLIST_CREATED);
//        alreadyExistPickingDetail.setPickingSummaryId(pickingSummaryId);
//        alreadyExistPickingDetail.setAssignedTo(assignedTo);
//        alreadyExistPickingDetail.setBoxCode(null);
//        alreadyExistPickingDetail.setItemBarcode(null);
//        if(MDC.get("USER_ID")==null) {
//            alreadyExistPickingDetail.setCreatedBy(Constant.ADDVERB_CONSTANT.USER);
//            alreadyExistPickingDetail.setUpdatedBy(Constant.ADDVERB_CONSTANT.USER);
//        } else {
//            alreadyExistPickingDetail.setCreatedBy(MDC.get("USER_ID"));
//            alreadyExistPickingDetail.setUpdatedBy(MDC.get("USER_ID"));
//        }
//
//        alreadyExistPickingDetail.setUpdatedAt(new Date());
//        alreadyExistPickingDetail.setProductId(picklistOrderItem.getProductId());
//        alreadyExistPickingDetail.setShipmentId(picklistOrderItem.getShipmentId());
//        alreadyExistPickingDetail.setLocationBarcode(picklistOrderItem.getLocationBarcode());
//        alreadyExistPickingDetail.setSkippedCount(picklistOrderItem.getRepickCount() + 1);
//        //alreadyExistPickingDetail.setPickingCutoff(DateUtils.addHours(alreadyExistPickingDetail.getUpdatedAt(), 3));
//        pickingDetailList.add(alreadyExistPickingDetail);
//    }

//    private void createNewPickingDetail(String assignedTo, Long id, List<PickingDetail> pickingDetailList,
//                                        PicklistOrderItem pickList) {
//        PickingDetail newPickingDetail = pickList.toPickingDetail();
//        newPickingDetail.setAssignedTo(assignedTo);
//
////        pickingDetail.setProductQuantity(productCount);
//        newPickingDetail.setPickingSummaryId(id);
//        newPickingDetail.setStatus(Constant.PICKING_STATUS.PICKLIST_CREATED);
//        List<PickingDetail> pickingDetailsBox =
//                pickingDetailRepository.findOneByShipmentIdAndBoxCodeIsNotNull(pickList.getShipmentId());
//        if (pickingDetailsBox != null && !pickingDetailsBox.isEmpty()) {
//            newPickingDetail.setBoxCode(pickingDetailsBox.get(0).getBoxCode());
//        }
//        if (pickList != null && pickList.getFitting().equalsIgnoreCase(Constant.PICKING_DEFAULT.FITTING_REQUIRED_STATUS)) {
//            newPickingDetail.setQcRequired(true);
//        }
//        pickingDetailList.add(newPickingDetail);
//    }

    public PickingSummary savePickingSummaryNew(String assignedTo, String facility, int type) {
        Date date = new Date();
        PickingSummary pickingSummary = new PickingSummary(assignedTo, date, Constant.PICKING_STATUS.CREATED, Constant.PICKING_DEFAULT.TOTAL_ITEM, Constant.PICKING_DEFAULT.SKIPPED_ITEM,
                Constant.PICKING_DEFAULT.PICKED_ITEM, Constant.PICKING_DEFAULT.SINGLE_BOX_ITEM, Constant.PICKING_DEFAULT.MULTI_BOX_ITEM, type);
        pickingSummary.setUpdatedAt(new Timestamp(date.getTime()));
        if(MDC.get("USER_ID")==null) {
            pickingSummary.setCreatedBy(Constant.ADDVERB_CONSTANT.USER);
            pickingSummary.setUpdatedBy(Constant.ADDVERB_CONSTANT.USER);
        } else {
            pickingSummary.setCreatedBy(MDC.get("USER_ID"));
            pickingSummary.setUpdatedBy(MDC.get("USER_ID"));
        }
        pickingSummary.setFacility(facility);
        try {
            return pickingSummaryRepository.save(pickingSummary);
        } catch (PickingException e) {
            log.error(e.getMessage(), e);
            throw new PickingException(e.getMessage(), e.getPickingExceptionStatus());
        }
    }

    public WaveDetailEntity createWave(String facilityCode, long epochTime, PickingSummary pickingSummary,
                                       GroupType groupType, OrderEventType eventType, String groupId,
                                       String idempotencyKey, WavePickingItem wavePayload,
                                       WaveOrderType waveOrderType) throws JsonProcessingException {

        WaveDetailEntity waveDetail = new WaveDetailEntity();
        waveDetail.setWave(pickingSummary);
        waveDetail.setWaveStatus(WaveStatus.NEW);
        waveDetail.setGroupId(groupId);
        waveDetail.setGroupType(groupType);
        waveDetail.setOrderEventType(eventType);
        waveDetail.setCreatedBy(Constant.ADDVERB_CONSTANT.USER + "_" + epochTime);
        waveDetail.setUpdatedBy(Constant.ADDVERB_CONSTANT.USER + "_" + epochTime);
        waveDetail.setFacility(facilityCode);
        waveDetail.setCreatedAt(Instant.now());
        waveDetail.setUpdatedAt(Instant.now());
        waveDetail.setRequest(mapper.writeValueAsString(wavePayload));
        waveDetail.setIdempotencyKey(idempotencyKey);
        waveDetail.setEventOrderType(eventType.name()+"_"+waveOrderType.name());
        waveDetailRepository.save(waveDetail);
        log.info("Wave created locally. Wave id is: {} | waveDetail:{}" , waveDetail.getId(),waveDetail);
        return waveDetail;
    }

    public AdverbWaveResponse buildWaveResponse(AddverbWaveRequestEntity waveRequestEntity) {
        AdverbWaveResponse adverbWaveResponse = new AdverbWaveResponse();
        adverbWaveResponse.setWaveId(waveRequestEntity.getWave().getId());
        adverbWaveResponse.setId(waveRequestEntity.getId());
        adverbWaveResponse.setStatus(waveRequestEntity.getStatus());
        adverbWaveResponse.setMessage(waveRequestEntity.getMessage());
        adverbWaveResponse.setShipmentSyncedCount(waveRequestEntity.getSyncedCount());
        adverbWaveResponse.setRequestedCount(waveRequestEntity.getRequestedCount());
        return adverbWaveResponse;
    }

    public List<WaveDetailItem> buildWaveDetailItems(List<WaveDetailEntity> waveDetail) {
        return waveDetail.stream().map(this::buildWaveDetail).collect(Collectors.toList());
    }

    public WaveDetailItem buildWaveDetail(WaveDetailEntity waveDetail) {
        WaveDetailItem waveDetailItem = new WaveDetailItem();
        waveDetailItem.setWaveStatus(waveDetail.getWaveStatus());
        waveDetailItem.setId(waveDetail.getId());
        waveDetailItem.setGroupId(waveDetail.getGroupId());
        waveDetailItem.setLog(waveDetail.getLog());
        return waveDetailItem;
    }

    @Logging
    public boolean isInventoryAvailable(List<PicklistOrderItem> picklistOrderItems, boolean isASRSInventoryCheck) {
        try {
            log.info("isInventoryAvailable for IMS shipment:{}", picklistOrderItems.get(0).getShipmentId());
            FetchStockDetailsRequest fetchStockDetailsRequest = new FetchStockDetailsRequest();
            List<BarcodeItemRequest> barcodeItemRequests = new ArrayList<>();
            Map<Integer,Boolean> pidMap = new ConcurrentHashMap<>();
            for(PicklistOrderItem picklistOrderItem:picklistOrderItems) {
                pidMap.put(picklistOrderItem.getProductId(),Boolean.FALSE);
                barcodeItemRequests.add(pickingCommonUtils.buildBarCodeRequest(picklistOrderItem,
                        isASRSInventoryCheck));
            }
            fetchStockDetailsRequest.setBarcodeItemRequests(barcodeItemRequests);
            FetchStockDetailsResponse imsFetchStockDetailsResponse = imsConnector.fetchStockDetails(fetchStockDetailsRequest);
            if (imsFetchStockDetailsResponse.getBarcodeItemRequestList() != null &&
                    !imsFetchStockDetailsResponse.getBarcodeItemRequestList().isEmpty()) {
                pidMap.forEach((k,v)->{
                    imsFetchStockDetailsResponse.getBarcodeItemRequestList()
                            .stream().filter(barcodeItemRequest -> k.intValue() == barcodeItemRequest.getPid())
                            .findFirst().ifPresent(barcodeItemRequest -> {
                                pidMap.put(k,Boolean.TRUE);
                            });
                });
                boolean isAnyPidIvnNotAvailable = pidMap.values().stream().anyMatch(item-> Boolean.FALSE == item);
                log.info("isAnyPidIvnNotAvailable :{}",isAnyPidIvnNotAvailable);
                log.info("pidMap for shipmentId {} from ims is {}", picklistOrderItems.get(0).getShipmentId(),pidMap.toString());
                return !isAnyPidIvnNotAvailable;
            } else {
                log.error("ERROR: No barcode exist in ims for location barcode for order item " + picklistOrderItems.toString());
            }
        } catch (Exception e) {
            log.error("ERROR: Fetching details from ims for location barcode for order item " + picklistOrderItems.toString() +
                    e.getMessage());
        }
        return false;
    }

    public void publishToKafkaAsList(List<WaveMessage> waveMessages) {
        log.info("publishToKafkaAsList: {}",waveMessages);
        waveMessages.forEach(this::publishToKafka);
        log.info("publishToKafkaAsList completed");
    }

    public OrderEventType findOrderEventType(List<PicklistOrderItem> picklistOrderItems, int size) {
        OrderEventType orderEventType = OrderEventType.NONE;
        boolean isAnyItemQCFailed =
                picklistOrderItems.stream().anyMatch(item -> OrderEventType.QC_FAILED.name().equalsIgnoreCase(item.getRepickStatus()));
        if (isAnyItemQCFailed) {
            orderEventType = OrderEventType.QC_FAILED;
        } else if (picklistOrderItems.size() == size) {
            orderEventType = OrderEventType.STANDARD;
        }
//        else if (picklistOrderItems.size() < size) {
//            orderEventType = isAnyItemQCFailed ? OrderEventType.QC_FAILED : OrderEventType.NONE;
//                    PicklistOrderItem picklistOrderItem = picklistOrderItems.get(0);
//                    if (Objects.nonNull(picklistOrderItem.getRepickStatus())) {
//                        orderEventType = OrderEventType.valueOf(picklistOrderItem.getRepickStatus());
//                    }
//        }

        if(OrderEventType.NONE.equals(orderEventType) && picklistOrderItems.size() < size) {
            orderEventType = OrderEventType.PARTIAL_PICK;
        }
        log.info("findOrderEventType: {} for : pickingList:{}", orderEventType, picklistOrderItems);
        return orderEventType;
    }

    public AddverbWaveRequestDto mapToAddverbRequestDto(AddverbWaveRequestEntity adverbWaveRequestEntity) {
        AddverbWaveRequestDto addverbWaveRequestDto = new AddverbWaveRequestDto();
        BeanUtils.copyProperties(adverbWaveRequestEntity, addverbWaveRequestDto);
        return addverbWaveRequestDto;
    }

    public GroupType findGroupType(int fittingId) {
        return fittingId > 0 ? GroupType.FITTING_TYPE : GroupType.SHIPMENT_TYPE;
    }

    @Logging
    public Date getAddverbPickingAllowTime(){
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofMinutes(pickingConfig.getAddverbAllowTimeInMinutes()));
        return Date.from(diffTime);
    }
    public Date getAddverbPickingSoppageTime(){
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofMinutes(pickingConfig.getAddverbStoppageTimeInMinutes()));
        return Date.from(diffTime);
    }

    public Date getAddverbPickingAllowTimeForJIT(){
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofMinutes(pickingConfig.getAddverbJitAllowTimeInMinutes()));
        return Date.from(diffTime);
    }

    public Date getAddverbPickingAllowTimeForQCFailJIT(){
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofMinutes(pickingConfig.getAddverbQcFailJitAllowTimeInMinutes()));
        return Date.from(diffTime);
    }
    public Date getAddverbPickingAllowTimeForQCFailFR0AndBulk(){
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofMinutes(pickingConfig.getAddverbQcFailFr0AndBulkAllowTimeInMinutes()));
        return Date.from(diffTime);
    }

    public Date getAddverbPickingAllowTimeForQCFail(){
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofMinutes(pickingConfig.getAddverbQCFailAllowTimeInMinutes()));
        return Date.from(diffTime);
    }

    public Date createAddverbCutOffTime(Date currentPickingCutOff){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentPickingCutOff);
        calendar.add(Calendar.MINUTE, pickingConfig.getPriorityPickingCutOffTimeDiffInMinutes());
        return calendar.getTime();
    }
    public Date createAddverbCutOffTimeForNDD(Date currentPickingCutOff){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentPickingCutOff);
        calendar.add(Calendar.MINUTE, pickingConfig.getPriorityNDDPickingCutOffTimeDiffInMinutes());
        return calendar.getTime();
    }
    public Date createAddverbCutOffTimeForNDDGold(Date currentPickingCutOff){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentPickingCutOff);
        calendar.add(Calendar.MINUTE, pickingConfig.getPriorityNDDGoldPickingCutOffTimeDiffInMinutes());
        return calendar.getTime();
    }
    public Date createAddverbCutOffTimeForNDDGoldMax(Date currentPickingCutOff){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentPickingCutOff);
        calendar.add(Calendar.MINUTE, pickingConfig.getPriorityNDDGoldMaxPickingCutOffTimeDiffInMinutes());
        return calendar.getTime();
    }
    public Date createAddverbCutOffTimeForP11(Date currentPickingCutOff){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentPickingCutOff);
        calendar.add(Calendar.MINUTE, pickingConfig.getPriority11PickingCutOffTimeDiffInMinutes());
        return calendar.getTime();
    }

    public Date getAddverbWaveCloseTime(){
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofHours(pickingConfig.getAddverbCloseWaveTime()));
        return Date.from(diffTime);
    }
    public Date getAddverbWaveCloseMaxTime(){
        int maxAllowTime = pickingConfig.getAddverbCloseWaveMaxTime();
        if(maxAllowTime<=pickingConfig.getAddverbCloseWaveTime()) {
            log.info("[AddverbUtils, getAddverbWaveCloseMaxTime] The max time {} is less than close wave time {} hence setting it to default 5 hrs extra",maxAllowTime,pickingConfig.getAddverbCloseWaveTime());
            maxAllowTime = pickingConfig.getAddverbCloseWaveTime()+5;
        }
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofHours(maxAllowTime));
        return Date.from(diffTime);
    }

//    public WaveMessage createWaveMessage(List<PicklistOrderItem> picklistOrderItemList, PickingSummary pickingSummary,
//                                          GroupType groupType, long epochTimeMills, OrderEventType orderEventType) {
//        try {
////            PicklistOrderItem picklistOrderItem = picklistOrderItems.get(0);
//            log.info("The picklistOrderItemList are {}", picklistOrderItemList);
////            OrderEventType orderEventType = adverbUtils.findOrderEventType(picklistOrderItems, picklistOrderItem.getNoItemPerOrder());
////            groupType = findGroupType(picklistOrderItem.getFittingId());
//            WaveMessage waveMessage = updatePickingDetailAndCreateWaveDetail(pickingSummary,
//                    picklistOrderItemList, epochTimeMills, groupType, orderEventType);
//            log.info("updatePickingDetail waveMessage:{} | for shipment:{}", waveMessage,
//                    picklistOrderItemList.get(0).getShipmentId());
//            return waveMessage;
//        } catch (Exception ex) {
//            log.error("Error on syncing picklistOrderItem:" + picklistOrderItemList + " | error message:" + ex.getMessage(), ex);
//        }
//        return null;
//    }


    public List<WaveMessage> createWaveMessageUsingPickListOrderItem(long epochTimeMills, PickingSummary pickingSummary, int remainingCount, Map<String, List<PicklistOrderItem>> picklistOrderItemMap, List<PicklistOrderItem> picklistOrderItemList, String pickingType,Map<Integer, Integer> asrsInventoryCache) {
        List<WaveMessage> waveMessageList = new ArrayList<>();
        for (PicklistOrderItem picklistOrderItem : picklistOrderItemList) {
            log.info("picklistOrderItemMap  valueee :{}", picklistOrderItem);
            GroupType groupType = findGroupType(picklistOrderItem.getFittingId());
            String key = buildAndGetKey(picklistOrderItem,groupType);
            if (!picklistOrderItemMap.containsKey(key)) {
                // add new shipment into map
                picklistOrderItemMap.put(key, new ArrayList<>());
            }
            addItemToShipmetMap(picklistOrderItemMap, picklistOrderItem, key);

            log.info("picklistOrderItemMap  key :{}, value:{}", key, picklistOrderItemMap.get(key));
            log.info("picklistOrderItemMap  key :{}, valueSize :{} , noOfItems : {}  " , key, picklistOrderItemMap.get(key).size(),picklistOrderItem.getNoItemPerOrder());

            if (picklistOrderItemMap.get(key).size() == picklistOrderItem.getNoItemPerOrder()) {
                List<PicklistOrderItem> picklistOrderItems = picklistOrderItemMap.get(key);
//                if(isUnffOrder(picklistOrderItems)) {
//                    continue;
//                }
                if (enablePartialPicking && allowedWaveTypeForPartial.contains(pickingType)) {
                    picklistOrderItems = picklistOrderItems.stream()
                            .filter(item -> Constant.PICKING_CONSTANT.ASRS.equalsIgnoreCase(item.getLocationType()))
                            .collect(Collectors.toList());
                }
                OrderEventType orderEventType = findOrderEventType(picklistOrderItems, picklistOrderItem.getNoItemPerOrder());
                WaveDetailEntity waveDetail = null;
                if(findIsSalveDBAllowed(pickingType))
                       waveDetail = waveDetailRepository.findByGroupIdAndCreatedAtGreaterThanMinutes(key,waveDetailsCreatedAtIntervalMinutes);
                log.info("[{}, ]The wave details fetched for groupId {} and isSlaveDBAllowedForAdverbSync {} is {}",
                        this.getClass().getSimpleName(), key, findIsSalveDBAllowed(pickingType),waveDetail);
                if(null == waveDetail) {
                    WaveMessage waveMessage = wavePayloadUtil.createWaveMessage(picklistOrderItems, pickingSummary, groupType, epochTimeMills, orderEventType,asrsInventoryCache);

                    if (Objects.nonNull(waveMessage)) {
                        waveMessageList.add(waveMessage);
                        if (waveMessageList.size() >= remainingCount)
                            break;
                    }
                }

                picklistOrderItemMap.remove(key);
            }
        }
        return waveMessageList;
    }

    private boolean isUnffOrder(List<PicklistOrderItem> picklistOrderItems) {
        return picklistOrderItems.stream()
                .anyMatch(item -> picklistOrderItemMetaDataRepository.fetchByPicklistOrderItemId(item.getId())
                        .stream()
                        .anyMatch(metaData -> Constant.PICKLIST_ORDER_META.TLP_UNFF_ORDER.equalsIgnoreCase(metaData.getPairKey())));
    }

    public boolean findIsSalveDBAllowed(String name) {
        try {
           return addverbSlaveMigrationConfig.getAddverbSlaveAllowed(name);
        }catch (Exception e) {
            log.error("[{}, fetchIsSalveDBAllowed] Unable to fetch if the slave DB is allowed for {} hence returning false",this.getClass().getSimpleName(),
                    name);
            return false;
        }
    }

    public Date getAddverbPickingAllowTimeForBulk() {
        Instant diffTime =
                Calendar.getInstance().getTime().toInstant().minus(Duration.ofMinutes(pickingConfig.getAddverbBulkAllowTimeInMinutes()));
        return Date.from(diffTime);
    }
}
